.test-room-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: #f0f2f5;
}

.test-controls {
  background: white;
  padding: 20px;
  border-bottom: 2px solid #e0e0e0;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.test-controls h2 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 1.5rem;
}

.test-buttons {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

.test-buttons button {
  padding: 10px 20px;
  border: 2px solid #ddd;
  background: white;
  color: #666;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.test-buttons button:hover {
  border-color: #4ecdc4;
  color: #4ecdc4;
}

.test-buttons button.active {
  background: #4ecdc4;
  border-color: #4ecdc4;
  color: white;
  box-shadow: 0 2px 8px rgba(78, 205, 196, 0.3);
}

.test-info {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  border-left: 4px solid #4ecdc4;
}

.test-info p {
  margin: 5px 0;
  color: #555;
  font-size: 0.95rem;
}

.test-info strong {
  color: #333;
}

.room-page-wrapper {
  flex: 1;
  position: relative;
}

/* Responsive Design */
@media (max-width: 768px) {
  .test-controls {
    padding: 15px;
  }
  
  .test-controls h2 {
    font-size: 1.3rem;
  }
  
  .test-buttons {
    gap: 8px;
  }
  
  .test-buttons button {
    padding: 8px 16px;
    font-size: 0.9rem;
  }
  
  .test-info {
    padding: 12px;
  }
  
  .test-info p {
    font-size: 0.9rem;
  }
}

import React, { useState } from 'react';
import StartPage from './pages/StartPage';
import RoomPage from './pages/RoomPage';
import HandGestureDetector from './components/HandGestureDetector';
import { BrowserRouter, Routes, Route, useParams } from 'react-router-dom';
import { P2PProvider } from './contexts/P2PContext';
import './style.css';

function App() {
  const [currentPage, setCurrentPage] = useState('start');
  const [userData, setUserData] = useState(null);
  const [roomData, setRoomData] = useState({
    id: 'demo-room-123',
    name: 'Test Room'
  });

  const handleNavigation = (page, data = null) => {
    setCurrentPage(page);
    if (data) {
      setUserData(data);
    }
  };

  const handleBackToStart = () => {
    setCurrentPage('start');
    setUserData(null);
  };

  const renderCurrentPage = () => {
    switch (currentPage) {
      case 'start':
        return <StartPage onNavigate={handleNavigation} />;
      case 'room':
        return (
          <RoomPage
            userData={userData}
            roomData={roomData}
            isOwner={true}
            gameState="waiting"
            onBack={handleBackToStart}
          />
        );
      case 'handGesture':
        return (
          <HandGestureDetector
            userData={userData}
            onBack={handleBackToStart}
          />
        );
      default:
        return <StartPage onNavigate={handleNavigation} />;
    }
  };

  return (
    <P2PProvider>
      <BrowserRouter>
        <div className="app">
          <Routes>
            <Route path="/" element={<StartPage onNavigate={handleNavigation} />} />
            <Route path="/room/:roomId" element={<RoomPageWrapper />} />
          </Routes>
        </div>
      </BrowserRouter>
    </P2PProvider>
  );
}

// Wrapper to extract roomId param and pass to RoomPage
function RoomPageWrapper() {
  const { roomId } = useParams();

  // For direct URL access (without P2P state), create a basic room
  const roomData = { id: roomId, name: `Room ${roomId}` };

  return (
    <RoomPage
      roomData={roomData}
      isOwner={false}
      gameState="waiting"
      onBack={() => window.history.back()}
    />
  );
}

export default App;

/* Start Page Styles */
.start-page {
  min-height: 100vh;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow-y: auto;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Background Animation */
.background-animation {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.floating-shapes {
  position: relative;
  width: 100%;
  height: 100%;
}

.shape {
  position: absolute;
  border-radius: 50%;
  opacity: 0.1;
  animation: float var(--duration, 8s) ease-in-out infinite;
  animation-delay: var(--delay, 0s);
}

.shape-0 {
  width: 80px;
  height: 80px;
  background: #ff6b6b;
  top: 20%;
  left: 10%;
}

.shape-1 {
  width: 60px;
  height: 60px;
  background: #4ecdc4;
  top: 60%;
  left: 80%;
}

.shape-2 {
  width: 100px;
  height: 100px;
  background: #45b7d1;
  top: 80%;
  left: 20%;
}

.shape-3 {
  width: 40px;
  height: 40px;
  background: #f9ca24;
  top: 30%;
  left: 70%;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

/* Main Container */
.start-container {
  position: relative;
  z-index: 10;
  max-width: 600px;
  width: 90%;
  text-align: center;
  opacity: 0;
  transform: translateY(50px);
  transition: all 1s cubic-bezier(0.4, 0, 0.2, 1);
}

.start-container.animate-in {
  opacity: 1;
  transform: translateY(0);
}

/* Header Section */
.header-section {
  margin-bottom: 3rem;
  padding: 2rem;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 20px;
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.2);

}

.app-title {
  font-size: 5rem;
  font-weight: 900;
  margin: 0;
  color: #ffffff;
  text-shadow:
    0 0 20px rgba(255, 255, 255, 0.8),
    0 0 40px rgba(255, 255, 255, 0.6),
    0 0 60px rgba(255, 255, 255, 0.4),
    0 4px 8px rgba(0, 0, 0, 0.3);
  letter-spacing: 2px;
  position: relative;
  z-index: 10;
}

.title-air {
  display: inline-block;
  animation: bounce 2s ease-in-out infinite;
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-right: 0.2em;
}

.title-draw {
  display: inline-block;
  animation: bounce 2s ease-in-out infinite 0.5s;
  background: linear-gradient(45deg, #4ecdc4, #45b7d1);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

@keyframes gradientShift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.app-subtitle {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.8);
  margin: 1rem 0 0 0;
  font-weight: 300;
}

/* Input Section */
.input-section {
  margin: 3rem 0;
}

.input-container {
  position: relative;
  max-width: 400px;
  margin: 0 auto;
}

.input-label {
  display: block;
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.1rem;
  margin-bottom: 1rem;
  font-weight: 500;
}

.name-input {
  width: 100%;
  padding: 1rem 1.5rem;
  font-size: 1.1rem;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 50px;
  color: white;
  text-align: center;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.name-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.name-input:focus {
  outline: none;
  border-color: #4ecdc4;
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 0 20px rgba(78, 205, 196, 0.3);
}

/* Gesture Preview */
.gesture-preview {
  margin: 2rem 0;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  justify-content: center;
  align-items: center;
  height: fit-content;
  width: 50vw;
  justify-self: center;
}

.hand-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 1rem;
}

.hand-svg {
  width: 100%;
  height: 100%;
  filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.3));
}

.hand-path {
  fill: rgba(255, 255, 255, 0.8);
  stroke: #4ecdc4;
  stroke-width: 2;
  animation: pulse 2s ease-in-out infinite;
}

.finger-tip {
  fill: #ff6b6b;
  animation: glow 1.5s ease-in-out infinite alternate;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes glow {
  from {
    opacity: 0.5;
  }
  to {
    opacity: 1;
  }
}

.gesture-text {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1rem;
  margin: 0;
  font-family: 'Courier New', monospace;
}

/* Action Buttons Section */
.button-section {
  display: flex;
  flex-direction: row;
  gap: 1rem;
  align-items: center;
  margin-top: 2rem;
}

/* Common button styles */
.create-button,
.join-button,
.tutorial-button {
  position: relative;
  padding: 0.8rem 2rem;
  border: none;
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  min-width: 200px;
  color: white;
}

/* Create Room Button specific styles */
.create-button {
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
}

/* Join Room Button specific styles */
.join-button {
  background: linear-gradient(45deg, #36d1dc, #5b86e5);
}

/* Tutorial Button specific styles */
.tutorial-button {
  background: linear-gradient(45deg, #a78bfa, #8b5cf6);
}

/* Common hover effects */
.create-button:hover,
.join-button:hover,
.tutorial-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

/* Common active effects */
.create-button:active,
.join-button:active,
.tutorial-button:active {
  transform: translateY(1px);
}

/* Sequential shimmer effect for buttons */
.create-button::before,
.join-button::before,
.tutorial-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    120deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  border-radius: inherit;
}

/* Sequential animation delays for flowing effect */
.create-button::before {
  animation: shimmerFlow 4.5s infinite linear;
  animation-delay: 0s;
}

.join-button::before {
  animation: shimmerFlow 4.5s infinite linear;
  animation-delay: 0.5s;
}

.tutorial-button::before {
  animation: shimmerFlow 4.5s infinite linear;
  animation-delay: 1s;
}

@keyframes shimmerFlow {
  0% {
    left: -100%;
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  20% {
    left: 100%;
    opacity: 1;
  }
  25% {
    opacity: 0;
  }
  100% {
    left: 100%;
    opacity: 0;
  }
}

/* Glow effect for all buttons */
.button-glow {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: inherit;
  filter: blur(20px);
  opacity: 0;
  transition: all 0.3s ease;
  z-index: -1;
}

.create-button:hover .button-glow,
.join-button:hover .button-glow,
.tutorial-button:hover .button-glow {
  opacity: 0.5;
  transform: scale(1.2);
}

/* Disabled state */
.create-button:disabled,
.join-button:disabled,
.tutorial-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Button text */
.button-text {
  position: relative;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

/* Media queries */
@media (max-width: 768px) {
  .create-button,
  .join-button,
  .tutorial-button {
    min-width: 180px;
    font-size: 1rem;
    padding: 0.7rem 1.8rem;
  }
}

@media (max-width: 480px) {
  .create-button,
  .join-button,
  .tutorial-button {
    min-width: 160px;
    font-size: 0.9rem;
    padding: 0.6rem 1.6rem;
  }
}

/* Features Section */
.features-section {
  margin-top: 4rem;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.feature-card {
  padding: 1.5rem 1rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.15);
}

.feature-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.feature-card h3 {
  color: white;
  font-size: 1rem;
  margin: 0.5rem 0;
  font-weight: 600;
}

.feature-card p {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
  margin: 0;
  line-height: 1.4;
}

/* Particles */
.particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 2;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  left: var(--x);
  top: var(--y);
  animation: particleFloat var(--duration, 4s) ease-in-out infinite;
  animation-delay: var(--delay, 0s);
}

@keyframes particleFloat {
  0%, 100% {
    opacity: 0;
    transform: translateY(0) scale(0);
  }
  50% {
    opacity: 1;
    transform: translateY(-100px) scale(1);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .app-title {
    font-size: 3rem;
  }
  
  .feature-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }
  
  .start-container {
    width: 95%;
  }
  
  .name-input {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .app-title {
    font-size: 2.5rem;
  }

  .feature-grid {
    grid-template-columns: 1fr;
  }
}

/* Feedback Message */
.feedback-message {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 1.5rem 2rem;
  border-radius: 15px;
  font-size: 1.1rem;
  font-weight: 600;
  text-align: center;
  z-index: 1000;
  backdrop-filter: blur(20px);
  border: 2px solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.3),
    0 0 30px rgba(255, 255, 255, 0.1);
  opacity: 0;
  transform: translate(-50%, -50%) scale(0.8);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  max-width: 90%;
  word-wrap: break-word;
}

.feedback-message.show {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1);
}

.feedback-message::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255, 107, 107, 0.1), rgba(78, 205, 196, 0.1));
  border-radius: 15px;
  z-index: -1;
}

.join-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.join-modal {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  padding: 2rem;
  border-radius: 20px;
  width: 90%;
  max-width: 400px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.join-modal h2 {
  color: white;
  margin: 0 0 1.5rem 0;
  font-size: 1.8rem;
  text-align: center;
}

.join-room-input-container {
  position: relative;
  margin-bottom: 1rem;
}

.join-room-input {
  width: 100%;
  padding: 1rem 3rem 1rem 1rem; /* Extra padding on right for eye icon */
  font-size: 1.1rem;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  color: white;
  box-sizing: border-box;
}

.join-room-input:focus {
  border-color: rgba(255, 255, 255, 0.4);
  outline: none;
}

.join-room-input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.toggle-visibility-btn {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.toggle-visibility-btn:hover {
  color: rgba(255, 255, 255, 0.9);
  background: rgba(255, 255, 255, 0.1);
}

.toggle-visibility-btn:focus {
  outline: none;
  background: rgba(255, 255, 255, 0.1);
}

.toggle-visibility-btn svg {
  width: 20px;
  height: 20px;
}

.join-error {
  color: #ff6b6b;
  background: rgba(255, 107, 107, 0.1);
  border: 1px solid rgba(255, 107, 107, 0.3);
  border-radius: 12px;
  padding: 16px 20px;
  margin-bottom: 1rem;
  text-align: left;
  font-size: 0.95rem;
  line-height: 1.5;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.2);
  animation: errorSlideIn 0.3s ease-out;
  white-space: pre-line;
  max-width: 100%;
  word-wrap: break-word;
}

@keyframes errorSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.join-modal-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.join-modal-join, .join-modal-cancel {
  padding: 0.8rem 2rem;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.join-modal-join {
  background: #4ecdc4;
  color: white;
}

.join-modal-cancel {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

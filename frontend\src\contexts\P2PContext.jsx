import React, { createContext, useContext, useState, useEffect } from 'react';

const P2PContext = createContext();

export const useP2P = () => {
  const context = useContext(P2PContext);
  if (!context) {
    throw new Error('useP2P must be used within a P2PProvider');
  }
  return context;
};

export const P2PProvider = ({ children }) => {
  const [p2pManager, setP2PManager] = useState(null);
  const [userData, setUserData] = useState(null);
  const [isOwner, setIsOwner] = useState(false);

  // Listen for ownership changes from P2PManager
  useEffect(() => {
    if (!p2pManager) return;

    const handleOwnershipChanged = ({ isOwner: newIsOwner }) => {
      console.log('👑 P2P Context: Ownership changed to:', newIsOwner);
      setIsOwner(newIsOwner);
    };

    p2pManager.on('ownershipChanged', handleOwnershipChanged);

    // Set initial ownership state
    setIsOwner(p2pManager.isOwner);

    return () => {
      p2pManager.off('ownershipChanged', handleOwnershipChanged);
    };
  }, [p2pManager]);

  const value = {
    p2pManager,
    setP2PManager,
    userData,
    setUserData,
    isOwner,
    setIsOwner
  };

  return (
    <P2PContext.Provider value={value}>
      {children}
    </P2PContext.Provider>
  );
};

export default P2PContext;

const express = require('express');
const http = require('http');
const cors = require('cors');
const { Server } = require('socket.io');

const app = express();
const server = http.createServer(app);
const io = new Server(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

app.use(cors());
app.use(express.json());

// Room management with active connections
const rooms = new Map(); // roomId -> {peers, createdAt, owner, settings, scores}

// Socket.IO connection handling
io.on('connection', (socket) => {
  //console.log(`Client connected: ${socket.id}`);

  // Handle room creation
  socket.on('createRoom', ({ roomId, userData }) => {
    //console.log(`Creating room: ${roomId}`);
    
    // Create new room entry
    rooms.set(roomId, {
      peers: new Map(),  // Will store peerId -> {socket, userData}
      createdAt: new Date(),
      owner: userData.peerId,
      settings: {
        players: 8,
        language: 'English',
        drawTime: 80,
        rounds: 3,
        gameMode: 'Normal',
        wordCount: 3,
        hints: 2,
        customWords: '',
        useCustomWordsOnly: false
      },
      scores: new Map()  // Will store peerId -> score
    });

    // Add the creator to the peers and scores maps
    const room = rooms.get(roomId);
    room.peers.set(userData.peerId, { socket, userData });
    room.scores.set(userData.peerId, 0);

    // Join socket.io room
    socket.join(roomId);
    
    // Store user data
    socket.userData = userData;
    socket.roomId = roomId;

    socket.emit('roomCreated', {
      success: true,
      roomId,
      peers: [],
      settings: room.settings,
      scores: Array.from(room.scores.entries())
    });
  });

  // Handle room joining
  socket.on('joinRoom', async ({ roomId, userData }) => {
    //console.log(`${userData.peerId} attempting to join room: ${roomId}`);

    const room = rooms.get(roomId);
    if (!room) {
      socket.emit('joinRoomError', { message: 'Room not found' });
      return;
    }

    // Check if room is full
    const maxPlayers = room.settings.players || 8;
    const currentPlayers = room.peers.size;

    if (currentPlayers >= maxPlayers) {
      socket.emit('joinRoomError', {
        message: `Room is full (${currentPlayers}/${maxPlayers} players)`
      });
      return;
    }

    // Add peer to room
    room.peers.set(userData.peerId, { socket, userData });
    room.scores.set(userData.peerId, 0);
    
    // Join socket.io room
    socket.join(roomId);
    
    // Store user data
    socket.userData = userData;
    socket.roomId = roomId;

    // Get list of existing peers
    const existingPeers = Array.from(room.peers.entries())
      .filter(([peerId]) => peerId !== userData.peerId)
      .map(([peerId, peer]) => ({
        peerId,
        userData: peer.userData
      }));

    // Notify the joining peer about existing peers and room state
    socket.emit('roomJoined', {
      success: true,
      roomId,
      peers: existingPeers,
      isOwner: room.owner === userData.peerId,
      settings: room.settings,
      scores: Array.from(room.scores.entries())
    });

    // Notify existing peers about the new peer
    socket.to(roomId).emit('peerJoined', {
      peerId: userData.peerId,
      userData,
      score: 0
    });
  });

  // Handle settings update
  socket.on('updateSettings', ({ roomId, settings }) => {
    const room = rooms.get(roomId);
    if (!room || room.owner !== socket.userData.peerId) return;

    room.settings = settings;
    
    // Broadcast to all peers in the room
    io.to(roomId).emit('settingsUpdated', { settings });
  });

  // Handle score update
  socket.on('updateScore', ({ roomId, peerId, score }) => {
    const room = rooms.get(roomId);
    if (!room) return;

    room.scores.set(peerId, score);
    
    // Broadcast to all peers in the room
    io.to(roomId).emit('scoreUpdated', { peerId, score });
  });

  // Handle WebRTC signaling
  socket.on('signal', ({ targetPeerId, signal }) => {
    const room = rooms.get(socket.roomId);
    if (!room) return;

    const targetPeer = room.peers.get(targetPeerId);
    if (!targetPeer) return;

    targetPeer.socket.emit('signal', {
      signal,
      fromPeerId: socket.userData.peerId
    });
  });

  // Handle disconnection
  socket.on('disconnect', () => {
    if (!socket.roomId || !socket.userData) return;

    const room = rooms.get(socket.roomId);
    if (!room) return;

    // Remove peer from room
    room.peers.delete(socket.userData.peerId);
    room.scores.delete(socket.userData.peerId);

    // Notify other peers about disconnection
    socket.to(socket.roomId).emit('peerLeft', {
      peerId: socket.userData.peerId
    });

    // If room is empty, remove it
    if (room.peers.size === 0) {
      rooms.delete(socket.roomId);
    }
    // If disconnected peer was owner, transfer ownership to oldest peer
    else if (room.owner === socket.userData.peerId) {
      const oldestPeer = Array.from(room.peers.entries())
        .sort(([, a], [, b]) => a.userData.joinTime - b.userData.joinTime)[0];
      
      if (oldestPeer) {
        const [newOwnerId] = oldestPeer;
        room.owner = newOwnerId;
        io.to(socket.roomId).emit('ownershipTransferred', {
          newOwner: newOwnerId
        });
      }
    }
  });
});

// Cleanup old rooms periodically
setInterval(() => {
  const now = Date.now();
  const maxAge = 24 * 60 * 60 * 1000; // 24 hours

  for (const [roomId, room] of rooms.entries()) {
    if (now - room.createdAt.getTime() > maxAge) {
      // Notify all peers in the room
      io.to(roomId).emit('roomExpired');
      
      // Remove all sockets from the room
      room.peers.forEach(socket => socket.leave(roomId));
      
      // Delete the room
      rooms.delete(roomId);
    }
  }
}, 60 * 60 * 1000); // Check every hour

const PORT = process.env.PORT || 3000;
server.listen(PORT, () => {
  //console.log(`🚀 AirDoodle Server running on port ${PORT}`);
  //console.log(`📡 WebRTC signaling server active`);
});
import React, { useEffect, useRef, useState } from 'react';
import './Camera.css';

const Camera = ({
  name,
  rank,
  points = 0,
  stream = null,
  isLocal = false,
  isOwner = false
}) => {
  const videoRef = useRef(null);
  const containerRef = useRef(null);
  const [containerWidth, setContainerWidth] = useState(0);

  // Get rank class based on rank value
  const getRankClass = (rank) => {
    if (rank === '1st') return 'rank-1st';
    if (rank === '2nd') return 'rank-2nd';
    if (rank === '3rd') return 'rank-3rd';
    return 'rank-other';
  };

  // Calculate dynamic font size based on container width
  const getDynamicFontSize = (width) => {
    if (width < 80) return 6;
    if (width < 120) return 8;
    if (width < 160) return 10;
    if (width < 200) return 12;
    if (width < 250) return 14;
    if (width < 300) return 16;
    return 18;
  };

  // ResizeObserver to track container size changes
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const resizeObserver = new ResizeObserver((entries) => {
      for (let entry of entries) {
        const { width } = entry.contentRect;
        setContainerWidth(width);

        // Apply dynamic font size as CSS custom property for fallback
        const fontSize = getDynamicFontSize(width);
        container.style.setProperty('--dynamic-font-size', `${fontSize}px`);

        //console.log(`Camera container resized: ${width}px, font-size: ${fontSize}px`);
      }
    });

    resizeObserver.observe(container);

    return () => {
      resizeObserver.disconnect();
    };
  }, []);

  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    /*console.log(`Camera ${name} (${isLocal ? 'local' : 'peer'}):`, {
      hasStream: !!stream,
      streamId: stream?.id,
      streamActive: stream?.active,
      videoTracks: stream?.getVideoTracks()?.length || 0,
      audioTracks: stream?.getAudioTracks()?.length || 0
    });*/

    if (stream) {
      video.srcObject = stream;

      // Use a timeout to ensure the video element is ready and still in DOM
      const playVideo = async () => {
        try {
          // Check if video is still in the DOM before playing
          if (video.isConnected && video.srcObject) {
            await video.play();
          }
        } catch (error) {
          // Only log error if it's not due to element being removed
          if (error.name !== 'AbortError') {
            console.error(`Error playing video for ${name}:`, error);
          }
        }
      };

      // Small delay to ensure video element is ready
      setTimeout(playVideo, 100);
    } else {
      // Clear video if no stream
      video.srcObject = null;
    }

    return () => {
      // Don't stop tracks for peer streams, only clear the video element
      if (video.srcObject && isLocal) {
        const tracks = video.srcObject.getTracks();
        tracks.forEach(track => track.stop());
      }
      if (video.srcObject) {
        video.srcObject = null;
      }
    };
  }, [stream, name, isLocal]);

  return (
    <div
      ref={containerRef}
      className="camera-container"
      style={{
        '--current-width': `${containerWidth}px`,
        '--dynamic-font-size': `${getDynamicFontSize(containerWidth)}px`
      }}
    >
      {stream ? (
        <video
          ref={videoRef}
          autoPlay
          playsInline
          muted={isLocal} // Mute local video to prevent feedback
          className="camera-video"
        />
      ) : (
        <div className="camera-no-stream">
          <div className="no-stream-message">
            {isLocal ? 'Camera starting...' : 'Connecting...'}
          </div>
        </div>
      )}
      <div className="camera-overlay">
        <div className="camera-top">
          <span className={`camera-rank ${getRankClass(rank)}`}>{rank}</span>
          <span className="camera-points">{points} pts</span>
        </div>
        <div className="camera-bottom">
          <div className="camera-name-container">
            {isOwner && (
              <span className="camera-crown" title="Room Owner">👑</span>
            )}
            <span className="camera-name">{name}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Camera;
/**
 * Word Extractor Utility
 * Extracts words from the word-options.csv file for the drawing game
 */

import wordOptionsCSV from '../resources/word-options.csv?raw';

/**
 * Extract words from the CSV file
 * @returns {string[]} Array of words/phrases for the drawing game
 */
export function extractWordsFromCSV() {
  try {
    // Split the CSV content into lines
    const lines = wordOptionsCSV.split('\n');
    
    // Remove the header line and empty lines
    const words = lines
      .slice(1) // Skip the first line (header: "Scribble Options")
      .map(line => line.trim()) // Remove whitespace
      .filter(line => line.length > 0) // Remove empty lines
      .filter(line => line !== 'Scribble Options'); // Remove any duplicate headers
    
    //console.log(`📝 Loaded ${words.length} words from CSV file`);
    return words;
  } catch (error) {
    console.error('❌ Error loading words from CSV:', error);
    
    // Fallback words if CSV loading fails
    return getFallbackWords();
  }
}

/**
 * Get a random word from the extracted words
 * @returns {string} A random word/phrase
 */
export function getRandomWord(words) {
  return words[Math.floor(Math.random() * words.length)];
}

/**
 * Get multiple random words (useful for word selection)
 * @param {number} count - Number of words to return
 * @returns {string[]} Array of random words
 */
export function getRandomWords(words, count) {
  const selectedWords = [];
  const usedIndices = new Set();
  
  while (selectedWords.length < count && selectedWords.length < words.length) {
    const randomIndex = Math.floor(Math.random() * words.length);
    
    if (!usedIndices.has(randomIndex)) {
      usedIndices.add(randomIndex);
      selectedWords.push(words[randomIndex]);
    }
  }
  
  return selectedWords;
}

/**
 * Fallback words if CSV loading fails
 * @returns {string[]} Array of fallback words
 */
function getFallbackWords() {
  return [
    'Cat', 'Dog', 'House', 'Tree', 'Car', 'Sun', 'Moon', 'Star',
    'Fish', 'Bird', 'Flower', 'Book', 'Phone', 'Computer', 'Chair',
    'Table', 'Apple', 'Banana', 'Pizza', 'Cake', 'Happy', 'Sad',
    'Running', 'Swimming', 'Dancing', 'Singing', 'Reading', 'Writing'
  ];
}

export default {
  extractWordsFromCSV,
  getRandomWord,
  getRandomWords,
  getFallbackWords
};

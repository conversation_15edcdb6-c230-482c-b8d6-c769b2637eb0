.drawer-view-container {
  position: relative;
  border-radius: 15px;
  overflow: hidden;
  background: #000;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  justify-self: center;
}

.drawer-video {
  border-radius: 15px;
  transform: scaleX(-1);

}

.drawer-canvas {
  border-radius: 15px;
}

.drawer-ui-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.timer-icon {
  font-size: 18px;
}

.timer-value {
  font-family: 'Courier New', monospace;
  font-size: 18px;
  color: #ffff00;
}

.drawer-name {
  position: absolute;
  top: 20px;
  left: 20px;
  background: rgba(78, 205, 196, 0.9);
  color: white;
  padding: 12px 20px;
  border-radius: 25px;
  font-weight: 600;
  font-size: 16px;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.3);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.current-word-display {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(255, 107, 107, 0.9);
  color: white;
  padding: 12px 24px;
  border-radius: 25px;
  font-weight: 700;
  font-size: 18px;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.3);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.drawer-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  z-index: 10;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

.loading-text {
  font-size: 18px;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .drawer-timer,
  .drawer-name {
    padding: 8px 16px;
    font-size: 14px;
  }
  
  .current-word-display {
    padding: 10px 20px;
    font-size: 16px;
  }
  
  .timer-value {
    font-size: 16px;
  }
}

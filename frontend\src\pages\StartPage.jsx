import React, { useState, useEffect, useRef } from 'react';
import './StartPage.css';
import ScaledHandGestureDetector from '../components/ScaledHandGestureDetector';
import { useNavigate, useLocation } from 'react-router-dom';
import P2PManager from '../services/P2PManager';
import { useP2P } from '../contexts/P2PContext';

const StartPage = () => {
  const location = useLocation();
  const [name, setName] = useState('');
  const [isAnimating, setIsAnimating] = useState(false);
  const [isCreatingRoom, setIsCreatingRoom] = useState(false);
  const [feedbackMessage, setFeedbackMessage] = useState('');
  const [showFeedback, setShowFeedback] = useState(false);
  const [showJoinModal, setShowJoinModal] = useState(false);
  const [joinRoomId, setJoinRoomId] = useState('');
  const [joinError, setJoinError] = useState('');
  const [showRoomId, setShowRoomId] = useState(false); // Toggle for showing/hiding room ID
  const { setP2PManager, setUserData, setIsOwner } = useP2P();
  const navigate = useNavigate();
  const gesturePreviewRef = useRef(null);
  const [gesturePreviewWidth, setGesturePreviewWidth] = useState(0);

  useEffect(() => {
    // Trigger entrance animation
    setIsAnimating(true);
    const savedName = localStorage.getItem('name');
    if (savedName) {
      setName(savedName);
    }
  }, []);

  // Handle redirected state
  useEffect(() => {
    if (location.state?.message) {
      showFeedbackMessage(location.state.message);
      // Clear the state after handling
      window.history.replaceState({}, document.title);
    }
  }, [location]);

  useEffect(() => {
    const node = gesturePreviewRef.current;
    if (!node) return;

    // Set initial width
    setGesturePreviewWidth(node.offsetWidth);

    // Create a ResizeObserver to watch for changes
    const resizeObserver = new window.ResizeObserver(entries => {
      for (let entry of entries) {
        if (entry.target === node) {
          setGesturePreviewWidth(entry.contentRect.width);
          //console.log('gesturePreviewWidth:', entry.contentRect.width);
        }
      }
    });
    resizeObserver.observe(node);

    // Cleanup
    return () => resizeObserver.disconnect();
  }, []);

  const showFeedbackMessage = (message, duration = 3000) => {
    setFeedbackMessage(message);
    setShowFeedback(true);
    setTimeout(() => {
      setShowFeedback(false);
    }, duration);
  };

  const copyToClipboard = async (text) => {
    try {
      await navigator.clipboard.writeText(text);
      return true;
    } catch (err) {
      console.error('Failed to copy to clipboard:', err);
      return false;
    }
  };
  const handleNameChange = (e) => {
    setName(e.target.value);
    localStorage.setItem('name', e.target.value);
    if (e.target.value.length > 0) {
      setIsCreatingRoom(false);
    }
  };
  const handleCreateRoom = async () => {
    if (!name.trim()) return;

    setIsCreatingRoom(true);

    try {
      const userData = {
        name: name.trim(),
        id: `user_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
      };

      // Create new P2P manager
      const p2pManager = new P2PManager();
      const roomInfo = await p2pManager.createRoom(userData);

      // Store in context with updated userData that includes peerId
      setP2PManager(p2pManager);
      setUserData(p2pManager.localUserData); // Use the userData with peerId
      setIsOwner(true);

      // Copy room ID to clipboard
      const copied = await copyToClipboard(roomInfo.roomId);

      if (copied) {
        showFeedbackMessage('🎉 Room created! Room ID copied to clipboard!');
      } else {
        showFeedbackMessage('✅ Room created!');
      }

      // Navigate to the new room page
      setTimeout(() => {
        navigate(`/room/${roomInfo.encryptedRoomId}`);
      }, 2000);

    } catch (error) {
      console.error('Error creating room:', error);
      showFeedbackMessage('❌ Failed to create room. Please try again.');
    } finally {
      setIsCreatingRoom(false);
    }
  };

  const handleTutorial = () => {
    navigate('tutorial');
  };

  const handleJoinRoom = async () => {
    if (!name.trim()) {
      setJoinError('Please enter your name before joining a room.');
      return;
    }
    if (!joinRoomId.trim()) {
      setJoinError('Please enter a room code.');
      return;
    }

    setJoinError('');

    try {
      const userData = {
        name: name.trim(),
        id: `user_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
      };

      // Create new P2P manager and join room
      const p2pManager = new P2PManager();
      await p2pManager.joinRoom(joinRoomId.trim(), userData);

      // Store in context with updated userData that includes peerId
      setP2PManager(p2pManager);
      setUserData(p2pManager.localUserData); // Use the userData with peerId
      setIsOwner(false);
      //console.log("p2pManager:", p2pManager.encryptedRoomId);
      // Navigate to the room page
      navigate(`/room/${p2pManager.encryptedRoomId}`);

      setShowJoinModal(false);
      setJoinRoomId('');

    } catch (error) {
      console.error('Error joining room:', error);

      // Handle specific error types with user-friendly messages
      const errorMessage = error.message || 'Unknown error';

      if (errorMessage.includes('Room is full')) {
        // Extract current/max players from error message like "Room is full (2/2 players)"
        const playerMatch = errorMessage.match(/\((\d+)\/(\d+) players\)/);
        if (playerMatch) {
          const [, current, max] = playerMatch;
          setJoinError(
            `🚫 Room is full (${current}/${max} players)\n\n` +
            `💡 Ask the room leader to:\n` +
            `• Go to game settings\n` +
            `• Increase "👥 Players" limit\n` +
            `• Or wait for someone to leave`
          );
        } else {
          setJoinError(
            `🚫 Room is full\n\n` +
            `💡 Ask the room leader to increase the player limit in game settings, or wait for someone to leave.`
          );
        }
      } else if (errorMessage.includes('not found') || errorMessage.includes('Invalid')) {
        setJoinError('❌ Invalid room code. Please check the code and try again.');
      } else if (errorMessage.includes('connection') || errorMessage.includes('network')) {
        setJoinError('🌐 Connection failed. Please check your internet and try again.');
      } else {
        setJoinError('❌ Failed to join room. Please try again.');
      }
    }
  };

  return (
    <div className="start-page">
      {/* Animated background */}
      <div className="background-animation">
        <div className="floating-shapes">
          {[...Array(20)].map((_, i) => (
            <div
              key={i}
              className={`shape shape-${i % 4}`}
              style={{
                '--delay': `${i * 0.5}s`,
                '--duration': `${8 + (i % 3) * 2}s`
              }}
            />
          ))}
        </div>
      </div>

      {/* Join Room Modal */}
      {showJoinModal && (
        <div className="join-modal-overlay">
          <div className="join-modal">
            <h2>Join Room</h2>
            <div className="join-room-input-container">
              <input
                type={showRoomId ? "text" : "password"}
                placeholder="Enter Room Code"
                value={joinRoomId}
                onChange={e => setJoinRoomId(e.target.value)}
                className="join-room-input"
                maxLength={20}
                autoFocus
              />
              <button
                type="button"
                className="toggle-visibility-btn"
                onClick={() => setShowRoomId(!showRoomId)}
                title={showRoomId ? "Hide room code" : "Show room code"}
              >
                {showRoomId ? (
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"/>
                    <line x1="1" y1="1" x2="23" y2="23"/>
                  </svg>
                ) : (
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                    <circle cx="12" cy="12" r="3"/>
                  </svg>
                )}
              </button>
            </div>
            {joinError && <div className="join-error">{joinError}</div>}
            <div className="join-modal-buttons">
              <button onClick={handleJoinRoom} className="join-modal-join">Join</button>
              <button onClick={() => {
                setShowJoinModal(false);
                setJoinError('');
                setShowRoomId(false); // Reset visibility when closing modal
              }} className="join-modal-cancel">Cancel</button>
            </div>
          </div>
        </div>
      )}

      {/* Feedback Message */}
      {showFeedback && (
        <div className={`feedback-message ${showFeedback ? 'show' : ''}`}>
          {feedbackMessage}
        </div>
      )}

      {/* Main content */}
      <div className={`start-container ${isAnimating ? 'animate-in' : ''}`}>
        {/* Header */}
        <div className="header-section">
          <h1 className="app-title">
            <span className="title-air">Air</span>
            <span className="title-draw">Doodle</span>
          </h1>
        </div>

        {/* Name input section */}
        <div className="input-section">
          <div className="input-container">
            <input
              id="name-input"
              type="text"
              value={name}
              onChange={handleNameChange}
              onKeyDown={(e) => e.key === 'Enter' && handleCreateRoom()}
              placeholder="Enter your name..."
              className="name-input"
              maxLength={20}
            />
            <div className="input-underline"></div>
          </div>
        </div>

        {/* Create room and join room buttons */}
        <div className="button-section">
          <button
            className="create-button"
            onClick={handleCreateRoom}
            disabled={!name.trim() || isCreatingRoom}
          >
            <div className="button-text">
              {isCreatingRoom ? 'Creating...' : 'Create a Room'}
            </div>
            <div className="button-glow"></div>
          </button>

          <button
            className="join-button"
            onClick={() => setShowJoinModal(true)}
            disabled={!name.trim()}
          >
            <div className="button-text">Join Room</div>
            <div className="button-glow"></div>
          </button>

          <button className="tutorial-button" onClick={handleTutorial}>
            <div className="button-text">
              View Tutorial
            </div>
            <div className="button-glow"></div>
          </button>
        </div>

        {/* Hand gesture preview */}
        <div className="gesture-preview" ref={gesturePreviewRef}>
          <ScaledHandGestureDetector width={gesturePreviewWidth} height={(gesturePreviewWidth * 0.75)} />
        </div>

      </div>

      {/* Particles effect */}
      <div className="particles">
        {[...Array(50)].map((_, i) => (
          <div
            key={i}
            className="particle"
            style={{
              '--x': `${Math.random() * 100}%`,
              '--y': `${Math.random() * 100}%`,
              '--delay': `${Math.random() * 5}s`,
              '--duration': `${3 + Math.random() * 4}s`
            }}
          />
        ))}
      </div>
    </div>
  );
};

export default StartPage;

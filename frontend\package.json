{"name": "air-draw-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@mediapipe/tasks-vision": "^0.10.22-rc.20250304", "dotenv": "^16.5.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^7.6.2", "socket.io-client": "^4.7.2", "three": "^0.176.0", "tinycolor2": "^1.6.0"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "eslint": "^8.45.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "vite": "^6.3.5"}}
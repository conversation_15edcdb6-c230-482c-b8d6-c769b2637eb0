export const encryptRoomId = (roomId) => {
  try {
    // Convert roomId to base64 and add some random salt
    const salt = Math.random().toString(36).substring(2, 8);
    const combined = `${roomId}:${salt}`;
    const encoded = btoa(combined);
    
    // Add some visual noise to make it look more encrypted
    return encoded
      .split('')
      .map(char => char + Math.random().toString(36).charAt(2))
      .join('')
      .toUpperCase();
  } catch (error) {
    console.error('Error encrypting room ID:', error);
    return roomId;
  }
};

export const decryptRoomId = (encryptedId) => {
  try {
    // Remove the visual noise (every other character)
    const encoded = encryptedId
      .split('')
      .filter((_, i) => i % 2 === 0)
      .join('');
    
    // Decode from base64 and extract the original room ID
    const decoded = atob(encoded);
    const [roomId] = decoded.split(':');
    return roomId;
  } catch (error) {
    console.error('Error decrypting room ID:', error);
    return encryptedId;
  }
}; 
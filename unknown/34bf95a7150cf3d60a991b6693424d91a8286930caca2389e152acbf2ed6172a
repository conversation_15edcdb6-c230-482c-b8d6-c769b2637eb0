.hand-gesture-container {
  position: relative;
  width: 640px;
  height: 480px;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  background-color: #000;
}

.video-element {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.canvas-element {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  transform: scaleX(-1);

}

.gesture-status-list {
  position: absolute;
  bottom: 20px;
  left: 20px;
  background: rgba(0,0,0,0.7);
  color: #fff;
  padding: 12px 20px;
  border-radius: 12px;
  z-index: 12;
  min-width: 180px;
}
.gesture-status-list h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: bold;
}
.gesture-status-list ul {
  list-style: none;
  padding: 0;
  margin: 0;
}
.gesture-status-list li {
  font-size: 15px;
  margin-bottom: 4px;
  transition: color 0.2s, font-weight 0.2s;
}
.gesture-status-list li.active-gesture {
  color: #00ff00;
  font-weight: bold;
}

.gesture-display {
  position: absolute;
  top: 20px;
  left: 20px;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 10px 20px;
  border-radius: 20px;
  font-size: 18px;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.3s ease;
  z-index: 10;
}

/* Gesture-specific styles */
.gesture-display.open-palm {
  background-color: rgba(0, 255, 0, 0.7);
  transform: scale(1.1);
}

.gesture-display.closed-fist {
  background-color: rgba(255, 0, 0, 0.7);
  transform: scale(1.1);
}

.gesture-display.pointing {
  background-color: rgba(0, 0, 255, 0.7);
  transform: scale(1.1);
}

.gesture-display.peace-sign {
  background-color: rgba(255, 165, 0, 0.7);
  transform: scale(1.1);
}

.gesture-display.rock-sign {
  background-color: rgba(128, 0, 128, 0.7);
  transform: scale(1.1);
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 20;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 5px solid #f3f3f3;
  border-top: 5px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-overlay p {
  color: white;
  font-size: 18px;
  margin: 0;
}

/* Three.js overlay canvas */
.three-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 25;
  pointer-events: none;
}

.eraser-visual {
  pointer-events: none;
  backdrop-filter: blur(6px);
  background: linear-gradient(135deg, rgba(126,203,255,0.18) 0%, rgba(30,144,255,0.22) 100%);
  border: 1.5px solid rgba(30,144,255,0.25);
  box-shadow: 0 2px 24px 6px #1e90ff33, 0 0 0 1.5px #fff3 inset;
  transition: left 0.08s, top 0.08s, width 0.08s, height 0.08s, opacity 0.2s, box-shadow 0.3s;
  animation: eraser-glow-modern 1.2s infinite alternate;
}
.color-wheel , .thickness{
  display: flex;
  border-radius: 50%;
  border: 3px solid #c5c3c371;
  background-color: #c5c3c371;
  width: 55px;
  height: 55px;
  align-items: center;
  justify-content: center;
}
.color-wheel-icon ,.thickness-icon {
  width: 50px;
  height: 50px;
}

@keyframes eraser-glow-modern {
  0% {
    box-shadow: 0 2px 24px 6px #1e90ff33, 0 0 0 1.5px #fff3 inset;
    opacity: 0.7;
  }
  100% {
    box-shadow: 0 4px 36px 12px #7ecbff55, 0 0 0 2.5px #fff5 inset;
    opacity: 0.92;
  }
}

/* Color Picker Styles */
.color-picker {
  background: rgba(30,30,30,0.85);
  border-radius: 18px;
  border: 2.5px solid #888;
  box-shadow: 0 4px 24px 0 #0006;
  z-index: 200;
  display: flex;
  align-items: center;
}
.color-picker__canvas {
  border-radius: 14px;
  box-shadow: 0 0 8px #000a;
  pointer-events: none;
}
.color-picker__selector {
  border-radius: 50%;
  border: 2.5px solid #bbb;
  background: rgba(255,255,255,0.15);
  box-shadow: 0 0 8px #000a;
  pointer-events: none;
}
.color-picker__slider {
  border-radius: 12px;
  border: 2px solid #888;
  box-shadow: 0 0 8px #000a;
  pointer-events: none;
}
.color-picker__slider-handle {
  border-radius: 8px;
  background: #fff;
  border: 2px solid #bbb;
  opacity: 0.7;
  box-shadow: 0 0 8px #000a;
  pointer-events: none;
}
.color-picker__preview {
  border-radius: 50%;
  border: 2.5px solid #888;
  box-shadow: 0 0 8px #000a;
  pointer-events: none;
}
.color-preview-circle {
  border-radius: 50%;
  border: 2.5px solid #888;
  box-shadow: 0 0 8px #000a;
}

.thickness-picker {
  background: rgba(30,30,30,0.92);
  border-radius: 18px;
  border: 2.5px solid #888;
  box-shadow: 0 4px 24px 0 #0006;
  z-index: 200;
  display: flex;
  align-items: center;
  flex-direction: column;
  position: absolute;
  padding: 12px 8px;
}
.thickness-picker__slider {
  position: relative;
  width: 32px;
  height: 180px;
  background: linear-gradient(to top, #fff, #222);
  border-radius: 16px;
  border: 2px solid #888;
  margin: 0 8px;
  box-shadow: 0 0 8px #000a;
}
.thickness-picker__handle {
  position: absolute;
  left: -6px;
  width: 52px;
  height: 18px;
  border-radius: 12px;
  background: #fff;
  border: 2px solid #bbb;
  opacity: 0.8;
  box-shadow: 0 0 8px #000a;
  pointer-events: none;
  align-self: center;
  justify-self: center;
}
.thickness-preview-circle {
  border-radius: 50%;
  border: 2.5px solid #888;
  box-shadow: 0 0 8px #000a;
  background: #fff;
  position: absolute;
  top: 72px;
  right: 32px;
  z-index: 120;
  display: flex;
  align-items: center;
  justify-content: center;
}
.finger-pointer {
  position: absolute;
  width: 22px;
  height: 22px;
  border-radius: 50%;
  background: rgba(255, 255, 0, 0.7);
  border: 2.5px solid #ff0;
  z-index: 300;
  pointer-events: none;
  box-shadow: 0 0 8px #ff0a;
}
.finger-pointer.index {
  background: rgba(0, 255, 255, 0.7);
  border-color: #0ff;
} 
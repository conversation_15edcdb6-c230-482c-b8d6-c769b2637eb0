import React from 'react';
import Camera from './Camera';
import './TestCamera.css';

const TestCamera = () => {
  const handleCameraToggle = (isOn) => {
    console.log('Camera is now:', isOn ? 'ON' : 'OFF');
  };

  return (
    <div className="test-container">
      <h1>Camera Component Test</h1>
      
      <div className="camera-grid">
        {/* Test different ranks */}
        <div className="camera-item">
          <h3>1st Place (Gold)</h3>
          <Camera 
            name="<PERSON> Johnson"
            rank="1st"
            points={250000}
            width={320}
            height={240}
            onCameraToggle={handleCameraToggle}
          />
        </div>

        <div className="camera-item">
          <h3>2nd Place (Silver)</h3>
          <Camera 
            name="<PERSON>"
            rank="2nd"
            points={180000}
            width={320}
            height={240}
            onCameraToggle={handleCameraToggle}
          />
        </div>

        <div className="camera-item">
          <h3>3rd Place (Bronze)</h3>
          <Camera 
            name="<PERSON> Brown"
            rank="3rd"
            points={120000}
            width={320}
            height={240}
            onCameraToggle={handleCameraToggle}
          />
        </div>

        <div className="camera-item">
          <h3>5th Place (Top 10)</h3>
          <Camera 
            name="Diana Prince"
            rank="5th"
            points={95000}
            width={320}
            height={240}
            onCameraToggle={handleCameraToggle}
          />
        </div>

        <div className="camera-item">
          <h3>25th Place (Top 50)</h3>
          <Camera 
            name="Edward Norton"
            rank="25th"
            points={45000}
            width={320}
            height={240}
            onCameraToggle={handleCameraToggle}
          />
        </div>

        <div className="camera-item">
          <h3>75th Place (Top 100)</h3>
          <Camera 
            name="Fiona Green"
            rank="75th"
            points={15000}
            width={320}
            height={240}
            onCameraToggle={handleCameraToggle}
          />
        </div>

        <div className="camera-item">
          <h3>150th Place (Others)</h3>
          <Camera 
            name="George Wilson"
            rank="150th"
            points={5000}
            width={320}
            height={240}
            onCameraToggle={handleCameraToggle}
          />
        </div>

        <div className="camera-item">
          <h3>Numeric Rank</h3>
          <Camera 
            name="Helen Davis"
            rank={8}
            points={75000}
            width={320}
            height={240}
            onCameraToggle={handleCameraToggle}
          />
        </div>
      </div>
    </div>
  );
};

export default TestCamera;

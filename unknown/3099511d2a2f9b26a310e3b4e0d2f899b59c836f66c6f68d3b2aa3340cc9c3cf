import React, { useState } from 'react';
import RoomPage from '../pages/RoomPage';
import './TestRoomPage.css';

const TestRoomPage = () => {
  const [testMode, setTestMode] = useState('owner-waiting');
  
  const userData = {
    name: '<PERSON>',
    id: 1
  };
  
  const roomData = {
    id: 'demo-room-123',
    name: 'Test Room'
  };

  const handleBack = () => {
    console.log('Back button clicked');
  };

  const getTestConfig = () => {
    switch (testMode) {
      case 'owner-waiting':
        return {
          isOwner: true,
          gameState: 'waiting',
          currentDrawer: null,
          title: 'Room Owner - Waiting State'
        };
      case 'owner-playing':
        return {
          isOwner: true,
          gameState: 'playing',
          currentDrawer: userData,
          title: 'Room Owner - Playing State (Drawing)'
        };
      case 'member-waiting':
        return {
          isOwner: false,
          gameState: 'waiting',
          currentDrawer: null,
          title: 'Room Member - Waiting State'
        };
      case 'member-playing':
        return {
          isOwner: false,
          gameState: 'playing',
          currentDrawer: { id: 2, name: '<PERSON>' },
          title: 'Room Member - Playing State (Watching)'
        };
      default:
        return {
          isOwner: true,
          gameState: 'waiting',
          currentDrawer: null,
          title: 'Default State'
        };
    }
  };

  const config = getTestConfig();

  return (
    <div className="test-room-container">
      {/* Test Controls */}
      <div className="test-controls">
        <h2>Room Page Test - {config.title}</h2>
        <div className="test-buttons">
          <button 
            className={testMode === 'owner-waiting' ? 'active' : ''}
            onClick={() => setTestMode('owner-waiting')}
          >
            Owner - Waiting
          </button>
          <button 
            className={testMode === 'owner-playing' ? 'active' : ''}
            onClick={() => setTestMode('owner-playing')}
          >
            Owner - Playing
          </button>
          <button 
            className={testMode === 'member-waiting' ? 'active' : ''}
            onClick={() => setTestMode('member-waiting')}
          >
            Member - Waiting
          </button>
          <button 
            className={testMode === 'member-playing' ? 'active' : ''}
            onClick={() => setTestMode('member-playing')}
          >
            Member - Playing
          </button>
        </div>
        
        <div className="test-info">
          <p><strong>Current Mode:</strong> {config.title}</p>
          <p><strong>Can Change Settings:</strong> {config.isOwner ? 'Yes' : 'No'}</p>
          <p><strong>Game State:</strong> {config.gameState}</p>
          <p><strong>Current Drawer:</strong> {config.currentDrawer?.name || 'None'}</p>
          <p><strong>Chat Enabled:</strong> {config.gameState === 'playing' ? 'Yes' : 'No'}</p>
        </div>
      </div>

      {/* Room Page Component */}
      <div className="room-page-wrapper">
        <RoomPage
          userData={userData}
          roomData={roomData}
          isOwner={config.isOwner}
          gameState={config.gameState}
          currentDrawer={config.currentDrawer}
          onBack={handleBack}
        />
      </div>
    </div>
  );
};

export default TestRoomPage;

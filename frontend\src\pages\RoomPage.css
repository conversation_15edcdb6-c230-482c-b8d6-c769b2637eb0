.room-page {
  height: 100vh;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  color: white;
  display: flex;
  flex-direction: column;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  overflow: hidden;
}

/* Header */
.room-header {
  height: 100px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 0 25px;
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  flex-shrink: 0;
}

.room-title h1 {
  font-size: 2.5rem;
  margin: 0;
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
  background-size: 300% 300%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradientShift 3s ease-in-out infinite;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.room-info {
  display: flex;
  gap: 20px;
  margin-top: 5px;
}

.round-info {
  background: rgba(255, 255, 255, 0.1);
  padding: 4px 12px;
  border-radius: 15px;
  font-size: 0.9rem;
  font-weight: 600;
}

.game-status {
  padding: 4px 12px;
  border-radius: 15px;
  font-size: 0.9rem;
  font-weight: 700;
  letter-spacing: 1px;
  transition: all 0.3s ease;
}

.game-status.waiting {
  background: rgba(255, 193, 7, 0.8);
  color: #333;
}

.game-status.playing {
  background: rgba(76, 175, 80, 0.8);
  color: white;
  animation: pulse 2s ease-in-out infinite;
}

.game-status.finished {
  background: rgba(244, 67, 54, 0.8);
  color: white;
}

/* Settings button removed - replaced with start button */

/* Main Content */
.room-content {
  height: calc(100% - 100px);
  display: flex;
  gap: 20px;
  width: 100%;
  padding: 20px;
  flex-shrink: 0;
  overflow: hidden;
}

/* Camera Grid - 2x5 layout on the left */
.camera-grid {
  width: 23%;
  height: 100%;
  flex-shrink: 0;
  display: grid;
  grid-template-columns: 49% 49%;
  grid-template-rows: repeat(5, 1fr);
  gap: 1%;
  align-items: stretch;
  justify-items: stretch;
  box-sizing: border-box;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 20px;
  box-shadow: 0 10px 10px rgba(0, 0, 0, 0.2);
  border-radius: 15px;  
  padding:20px
}

.camera-slot {
  border-radius: 15px;
  overflow: hidden;
  background: rgba(0, 0, 0, 0.3);
  border: 2px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

.camera-slot.user-camera {
  border-color: rgba(76, 175, 80, 0.8);
  box-shadow: 0 0 25px rgba(76, 175, 80, 0.4);
  background: rgba(76, 175, 80, 0.1);
  border-width: 3px;
  position: relative;
  width:100%;
}

.camera-slot.user-camera::before {
  position: absolute;
  top: 8px;
  left: 8px;
  background: rgba(76, 175, 80, 0.9);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 700;
  z-index: 10;
  letter-spacing: 0.5px;
}

.camera-slot:not(.user-camera) {
  height: 100%;
}

/* Camera component styling within slots */
.camera-slot .camera-container {
  margin: 0;
  padding: 8px;
  border-radius: 12px;
  box-sizing: border-box;
  background: transparent;
  border: none;
  box-shadow: none;
  width: 100%;
  aspect-ratio: 4/3;
}

.camera-slot .camera-container:hover {
  transform: none;
  box-shadow: none;
}

.camera-slot .camera-display {
  width: 100%;
  height: 100%;
  border-radius: 8px;
}

.camera-slot.empty {
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.05);
  border-style: dashed;
  min-width: 240px;
  min-height: 180px;
  width: 100%;
  height: 100%;
}

.empty-slot {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
  color: rgba(255, 255, 255, 0.5);
  font-size: 0.8rem;
  width: 100%;
  height: 100%;
  min-width: 240px;
  min-height: 180px;
  justify-content: center;
}

.empty-icon {
  font-size: 1.5rem;
  font-weight: bold;
}

/* Main Content Area - Centered */
.main-content {
  width: 60%;
  height: 100%;
  margin: 0 0;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 20px;
  color: #333;
  box-shadow: 0 10px 10px rgba(0, 0, 0, 0.2);
  padding-top: 20px;
  padding-bottom: 20px;
}

/* Chat Sidebar */
.chat-sidebar {
  width: 17%;
  height: 100%;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 10px 10px rgba(0, 0, 0, 0.2);
  overflow: hidden;
}

.chat-header {
  background: linear-gradient(45deg, #4ecdc4, #44a08d);
  color: white;
  padding: 15px 20px;
  font-weight: 700;
  font-size: 1.1rem;
  text-align: center;
  letter-spacing: 0.5px;
}

.chat-messages {
  padding: 15px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 10px;
  height: calc(100% - 70px);
}

/* Custom scrollbar for chat messages */
.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

.chat-message {
  padding: 8px 12px;
  border-radius: 12px;
  font-size: 0.9rem;
  line-height: 1.4;
  word-wrap: break-word;
}

.chat-message.system {
  background: rgba(108, 117, 125, 0.1);
  color: #6c757d;
  font-style: italic;
  text-align: center;
  border: 1px solid rgba(108, 117, 125, 0.2);
}

.chat-message.guess {
  background: rgba(33, 150, 243, 0.1);
  color: #1976d2;
  border-left: 3px solid #2196f3;
}

.chat-message.correct {
  background: rgba(76, 175, 80, 0.1);
  color: #388e3c;
  border-left: 3px solid #4caf50;
  font-weight: 600;
}

.chat-message.close {
  background: rgba(255, 193, 7, 0.1);
  color: #f57c00;
  border-left: 3px solid #ffc107;
}

.message-author {
  font-weight: 600;
  margin-right: 8px;
}

.message-text {
  color: #333;
}

.chat-input-container {
  padding: 15px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  background: rgba(248, 249, 250, 0.8);
}

.chat-input-field {
  width: 100%;
  height: 100%;
  padding: 12px 15px;
  border: 2px solid #e0e0e0;
  border-radius: 25px;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  background: white;
  bottom: 0px;  
}

.chat-input-field:focus {
  outline: none;
  border-color: #4ecdc4;
  box-shadow: 0 0 0 3px rgba(78, 205, 196, 0.1);
}

.chat-input-field:disabled {
  background: #f5f5f5;
  color: #999;
  cursor: not-allowed;
}

/* Game Settings */
.game-settings {
  border-radius: 15px;
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 15px;
  width: 100%;
  background: transparent;
  color: #f3f6fa;
  overflow-y: auto;
}

.settings-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  grid-template-rows: auto auto auto;
  gap: 15px;
  align-items: start;
  flex-shrink: 0;
}

.game-settings label {
  color: #cbe6ff;
  font-weight: 600;
  margin-bottom: 3px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.game-settings select,
.game-settings input[type="text"] {
  background: rgba(255,255,255,0.15);
  color: #ffffff;
  border: 1.5px solid #3a5a8a;
  border-radius: 10px;
  padding: 8px 12px;
  font-size: 1rem;
  margin-bottom: 4px;
  transition: border 0.2s, box-shadow 0.2s;
  box-shadow: 0 2px 8px 0 #0002;
  position: relative;
  z-index: 10;
}

.game-settings select {
  cursor: pointer;
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 12px center;
  background-size: 16px;
  padding-right: 40px;
}

.game-settings select option {
  background: #1a2332 !important;
  color: #ffffff !important;
  padding: 8px 12px;
  border: none;
  font-size: 1rem;
}

.game-settings select option:hover,
.game-settings select option:focus {
  background: #2a3442 !important;
  color: #ffffff !important;
}

.game-settings select option:checked {
  background: #4ecdc4 !important;
  color: #ffffff !important;
}

.game-settings select:focus,
.game-settings input[type="text"]:focus,
.game-settings textarea:focus {
  border: 1.5px solid #6ec6ff;
  outline: none;
  background: rgba(255,255,255,0.25);
  box-shadow: 0 0 0 3px rgba(110, 198, 255, 0.2);
  z-index: 20;
}

.game-settings textarea,
.custom-words-input {
  background: rgba(255,255,255,0.10);
  color: #eaf6ff;
  border: 1.5px solid #3a5a8a;
  border-radius: 12px;
  padding: 15px;
  font-size: 1.1rem;
  resize: vertical;
  box-shadow: 0 2px 8px 0 #0002;
  width: 100%;
}

.game-settings input[type="checkbox"] {
  accent-color: #4ecdc4;
  width: 18px;
  height: 18px;
  margin-right: 8px;
}

.game-settings .setting-group {
  background: rgba(30, 60, 120, 0.18);
  border-radius: 12px;
  padding: 12px;
  display: flex;
  flex-direction: column;
  height: fit-content;
  transition: all 0.3s ease;
}

.game-settings .setting-group:hover {
  background: rgba(30, 60, 120, 0.25);
  transform: translateY(-2px);
}

.game-settings .custom-words-section {
  background: rgba(30, 60, 120, 0.18);
  border-radius: 12px;
  padding: 15px;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0;
  height: 100%;
  margin-top: -10px;
}

.game-settings .custom-words-section:hover {
  background: rgba(30, 60, 120, 0.25);
}

.game-settings .custom-words-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
  color: #cbe6ff;
}

.game-settings .custom-words-header label:first-child {
  font-size: 1.1rem;
  font-weight: 700;
}

.game-settings .checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: color 0.3s ease;
}

.game-settings .checkbox-label:hover {
  color: #ffffff;
}

.game-settings .custom-words-input {
  background: rgba(255,255,255,0.10);
  color: #eaf6ff;
  border: 1.5px solid #3a5a8a;
  border-radius: 12px;
  padding: 15px;
  font-size: 1rem;
  box-shadow: 0 2px 8px 0 #0002;
  width: 100%;
  resize: vertical;
  flex: 1;
  min-height: 90px;
}

/* Game Canvas */
.game-canvas {
  width: 100%;
  height: 90%;
  border-radius: 15px;
  overflow: hidden;
  box-sizing: border-box;
}

/* Start Button - moved to header */

.start-btn {
  background: linear-gradient(45deg, #4CAF50, #45a049);
  color: white;
  border: none;
  padding: 15px 30px;
  border-radius: 25px;
  font-size: 1.2rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
  box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
  margin-top: 15px;
  width: fit-content;
  align-self: center;
  justify-self: flex-end;
}

.start-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
}

.start-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Header Buttons */
.header-buttons {
  display: flex;
  gap: 10px;
  align-self: center;
}

.leave-room-btn {
  background: linear-gradient(45deg, #ff4757, #ff3742);
  color: white;
  border: none;
  padding: 10px 15px;
  border-radius: 50%;
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(255, 71, 87, 0.3);
  width: 45px;
  height: 45px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.leave-room-btn:hover {
  background: linear-gradient(45deg, #ff3742, #ff2f3a);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 71, 87, 0.4);
}

.end-game-btn {
  background: linear-gradient(45deg, #ffa502, #ff6348);
  color: white;
  border: none;
  padding: 10px 15px;
  border-radius: 50%;
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(255, 165, 2, 0.3);
  width: 45px;
  height: 45px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.end-game-btn:hover {
  background: linear-gradient(45deg, #ff6348, #ff4757);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 165, 2, 0.4);
}

.invite-btn {
  background: linear-gradient(45deg, #2196F3, #1976D2);
  color: white;
  border: none;
  padding: 15px 30px;
  border-radius: 25px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
}

.invite-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(33, 150, 243, 0.4);
}

.chat-input {
  flex: 1;
  max-width: 400px;
}

.chat-input input {
  width: 100%;
  padding: 12px 20px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 25px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 0.95rem;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.chat-input input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.chat-input input:focus {
  outline: none;
  border-color: rgba(255, 255, 255, 0.4);
  background: rgba(255, 255, 255, 0.15);
}

.chat-input input:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Invite Modal */
.invite-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

.invite-content {
  background: white;
  padding: 25px;
  border-radius: 15px;
  text-align: center;
  color: #333;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  max-width: 400px;
  width: 90%;
}

.invite-content p {
  margin: 0 0 15px 0;
  font-weight: 600;
  color: #4CAF50;
}

.invite-content code {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 8px;
  display: block;
  word-break: break-all;
  font-size: 0.9rem;
  color: #666;
}

.hand-gesture-container {
  height: fit-content;
  width: fit-content;
  align-self: center;
  justify-self: center;
}

/* Animations */
@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@keyframes fadeIn {
  from { opacity: 0; transform: scale(0.9); }
  to { opacity: 1; transform: scale(1); }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(0.98);
  }
}

/* Responsive Design */
@media (max-width: 1200px) {
  .room-content {
    flex-direction: column;
  }

  .camera-grid {
    width: 100%;
    grid-template-columns: repeat(5, 1fr);
    grid-template-rows: repeat(2, 1fr);
    gap: 10px;
    margin-bottom: 20px;
  }

  .camera-slot {
    width: 10%;
  }

  .camera-slot.user-camera {
    height: 120px;
  }

  .chat-sidebar {
    width: 100%;
    max-height: 300px;
  }

  .settings-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }
}

@media (max-width: 768px) {
  .room-header {
    padding: 10px 15px;
  }

  .room-title h1 {
    font-size: 2rem;
  }

  .room-content {
    padding: 15px;
    gap: 15px;
  }

  .chat-sidebar {
    max-height: 250px;
  }

  .camera-grid {
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(4, 1fr);
    gap: 8px;
  }

  .camera-slot {
    height: 100px;
  }

  .room-actions {
    flex-wrap: wrap;
    gap: 10px;
    padding: 15px 20px;
  }

  .start-btn, .invite-btn {
    padding: 12px 25px;
    font-size: 0.95rem;
  }

  .game-top-bar {
    padding: 0 15px;
  }

  .game-timer {
    padding: 8px 14px;
    gap: 8px;
  }

  .timer-icon {
    font-size: 1.3rem;
  }

  .timer-value {
    font-size: 1.3rem;
    min-width: 55px;
    letter-spacing: 0.5px;
  }

  .game-word-display {
    font-size: 1.4rem;
  }
}

.game-top-bar {
  width: 100%;
  height: 10%;
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px 12px 0 0;
  margin-bottom: 10px;
  margin-top: -20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.game-timer {
  display: flex;
  align-items: center;
  gap: 10px;
  background: rgba(0, 0, 0, 0.4);
  padding: 10px 18px;
  border-radius: 25px;
  border: 2px solid rgba(255, 107, 107, 0.3);
  box-shadow:
    0 4px 15px rgba(255, 107, 107, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.timer-icon {
  font-size: 1.5rem;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
  margin-top: -9px;
  animation: timerPulse 2s ease-in-out infinite;
}

@keyframes timerPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.timer-value {
  font-family: 'Courier New', monospace;
  font-size: 1.6rem;
  font-weight: 900;
  color: #ff6b6b;
  text-shadow:
    0 2px 4px rgba(0, 0, 0, 0.5),
    0 0 10px rgba(255, 107, 107, 0.3);
  letter-spacing: 1px;
  min-width: 70px;
  text-align: center;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.timer-value.warning {
  color: #ffa502;
  animation: timerWarning 1s ease-in-out infinite;
}

.timer-value.critical {
  color: #ff4757;
  animation: timerCritical 0.5s ease-in-out infinite;
}

@keyframes timerWarning {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes timerCritical {
  0%, 100% {
    transform: scale(1);
    text-shadow:
      0 2px 4px rgba(0, 0, 0, 0.5),
      0 0 10px rgba(255, 71, 87, 0.5);
  }
  50% {
    transform: scale(1.1);
    text-shadow:
      0 2px 4px rgba(0, 0, 0, 0.5),
      0 0 20px rgba(255, 71, 87, 0.8);
  }
}

.game-word-display {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-size: 1.8rem;
  font-weight: 700;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  letter-spacing: 1px;
}

.drawer-word {
  color: #4ecdc4;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.guessing-word {
  color: #fff;
  font-family: 'Courier New', monospace;
  letter-spacing: 3px;
  font-weight: 700;
  font-size: 1.8rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Word Selection Waiting Styles */
.word-selection-waiting {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 15px;
  position: relative;
  overflow: hidden;
}

.word-selection-waiting::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.waiting-message {
  text-align: center;
  color: white;
  z-index: 1;
  position: relative;
}

.waiting-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  animation: pulse 2s infinite;
}

.waiting-text {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.waiting-timer {
  font-size: 1.2rem;
  opacity: 0.8;
  font-weight: 500;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.7;
  }
}

.game-top-bar-right {
  /* Reserved for future elements like hints, etc. */
  min-width: 100px;
}

/* Word Selection Status Bar */
.word-selection-status-bar {
  width: 100%;
  height: 10%;
  background: rgba(138, 43, 226, 0.3);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 15px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 15px;
}

.word-selection-message {
  color: white;
  font-size: 18px;
  font-weight: 600;
  text-align: center;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  letter-spacing: 0.5px;
}

.room-id-display {
  display: flex;
  align-self: center;
  gap: 8px;
  cursor: pointer;
  position: relative;
}

.room-id-text {
  font-weight: 700;
  font-size: 18px;
  color: #fff;
  background: #234;
  border-radius: 8px;
  padding: 6px 14px;
  transition: all 0.3s ease;
  user-select: none; /* Prevent automatic selection */
}

/* Only allow selection when not blurred */
.room-id-text:not(.blurred) {
  user-select: text;
}

.room-id-text.blurred {
  filter: blur(4px);
  background: #345;
}

.room-id-text:hover {
  background: #345;
}

.copy-button {
  background: #4CAF50;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 6px 14px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.copy-button:hover {
  background: #45a049;
  transform: translateY(-1px);
}

.copy-feedback {
  position: absolute;
  top: -30px;
  right: 0;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 14px;
  opacity: 0;
  transform: translateY(10px);
  transition: all 0.3s ease;
}

.copy-feedback.visible {
  opacity: 1;
  transform: translateY(0);
}

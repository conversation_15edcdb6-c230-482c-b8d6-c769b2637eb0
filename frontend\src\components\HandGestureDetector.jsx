import React, { useEffect, useRef, useState, useCallback, useMemo } from 'react';
import { FilesetResolver, HandLandmarker, FaceDetector } from '@mediapipe/tasks-vision';
import * as THREE from 'three';
import tinycolor from 'tinycolor2';

import './HandGestureDetector.css';
import colorWheel from '../resources/colorWheel.png';
import thickness from '../resources/thickness.png';

// ===== CONFIGURATION CONSTANTS =====
const GESTURE_TYPES = {
  NOTHING: 'Nothing',
  DRAWING: 'Drawing',
  ERASING: 'Erasing',
  MOVING: 'Moving',
  BACK: 'Back',
  SELECT: 'Select',
  PINCH: 'Pinch',
  SCALING: 'Scaling'
};

const GESTURE_OPTIONS = Object.values(GESTURE_TYPES);

// MediaPipe hand connections fallback
const HAND_CONNECTIONS = Hands.HAND_CONNECTIONS || [
  [0,1],[1,2],[2,3],[3,4],      // Thumb
  [0,5],[5,6],[6,7],[7,8],     // Index
  [5,9],[9,10],[10,11],[11,12],// Middle
  [9,13],[13,14],[14,15],[15,16],// Ring
  [13,17],[17,18],[18,19],[19,20],// Pinky
  [0,17]
];

// Performance configuration - optimized for responsiveness
const PERFORMANCE_CONFIG = {
  TARGET_FPS: 30, // Reduced from 60 to 30 for better performance
  MIN_FRAME_INTERVAL: 1000 / 30, // ~33ms between frames
  MODE_STABILITY_TIME: 30, // Reduced from 50ms for faster response
  HAND_LOCK_TIMEOUT: 50, // Reduced from 100ms for faster switching
  FACE_PROCESS_INTERVAL: 1000 / 15 // Process face at 15fps instead of every frame
};

// Hand tracking configuration
const HAND_TRACKING_CONFIG = {
  CONFIDENCE_THRESHOLD: 0.7,
  POSITION_SMOOTHING_FACTOR: 0.6,
  MAX_HISTORY_SIZE: 10,
  MOVING_SMOOTHING_FACTOR: 0.15,
  SCALING_SMOOTHING_FACTOR: 0.2
};

// Drawing configuration - optimized for performance
const DRAWING_CONFIG = {
  SMOOTHING_ALPHA: 0.3, // Reduced from 0.5 for faster response
  MIN_DISTANCE: 1.0, // Increased from 0.7 to reduce drawing frequency
  MAX_DISTANCE: 60,
  MAX_TIME_BETWEEN_POINTS: 100, // Reduced from 200ms for more responsive drawing
  THICKNESS_MIN: 1,
  THICKNESS_MAX: 48, // Increased max thickness
  DEFAULT_THICKNESS: 4,
  DRAW_THROTTLE: 16 // ~60fps drawing limit (16ms between draws)
};

// MediaPipe Tasks-Vision model URLs
const MEDIAPIPE_MODELS = {
  HAND_LANDMARKER: 'https://storage.googleapis.com/mediapipe-models/hand_landmarker/hand_landmarker/float16/1/hand_landmarker.task',
  FACE_DETECTOR: 'https://storage.googleapis.com/mediapipe-models/face_detector/blaze_face_short_range/float16/1/blaze_face_short_range.tflite'
};

// Gesture detection thresholds
const GESTURE_THRESHOLDS = {
  PINCH_THRESHOLD_SQ: 0.0042, // Increased by ~30%
  FINGER_EXTENSION_RATIO: 1.5,
  PINCH_DISTANCE: 65, // Increased by ~30%
  DUPLICATE_HAND_THRESHOLD: 0.008,
  HAND_POSITION_TOLERANCE: 0.3
};

// UI configuration
const UI_CONFIG = {
  CANVAS_WIDTH: 1280, // 4:3, higher than 960
  CANVAS_HEIGHT: 960, // 4:3, higher than 720
  COLOR_PICKER_SIZE: 350, // Scaled for new res
  COLOR_PICKER_SLIDER_WIDTH: 38, // Scaled
  COLOR_PICKER_MARGIN: 22, // Scaled
  THICKNESS_SLIDER_HEIGHT: 280, // Scaled
  THICKNESS_SLIDER_WIDTH: 44, // Scaled
  HEAD_TRACKING_ALPHA: 0.85
};

const HandGestureDetector = ({
  userData,
  onBack,
  width: propWidth,
  height: propHeight,
  scaledWidth,
  scaledHeight,
  scaleX,
  scaleY,
  disabled = false,
  // Word selection props
  isWordSelectionMode = false,
  wordOptions = [],
  onWordSelected = null,
  wordSelectionTimeLeft = 0,
  // Drawing data callback
  onDrawingData = null
}) => {
  /*console.log('🔄 COMPONENT: HandGestureDetector render with props:', {
    disabled,
    isWordSelectionMode,
    wordOptionsCount: wordOptions.length,
    wordSelectionTimeLeft
  });*/
  // ===== COMPONENT REFS =====
  const videoRef = useRef(null);
  const canvasRef = useRef(null);
  const handLandmarkerRef = useRef(null);
  const faceDetectorRef = useRef(null);
  const visionRef = useRef(null);
  const threeCanvasRef = useRef(null);
  const threeRendererRef = useRef(null);
  const threeSceneRef = useRef(null);
  const threeCameraRef = useRef(null);
  const animationFrameRef = useRef(null);

  // ===== STATE MANAGEMENT =====
  const [currentGestures, setCurrentGestures] = useState([]);
  const [handInfo, setHandInfo] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [mode, setMode] = useState('None');
  const [eraserVisual, setEraserVisual] = useState(null);

  // Head tracking state
  const [headBarPos, setheadBarPos] = useState(null);
  const headBarPosRef = useRef(null);
  const smoothedheadBarPos = useRef(null);

  // Color picker state
  const [isColorPicking, setIsColorPicking] = useState(false);
  const [colorPickerSelector, setColorPickerSelector] = useState({ x: 0.5, y: 0.5, v: 1 });
  const [selectedColor, setSelectedColor] = useState('#ffffff');
  const [colorPickerPos, setColorPickerPos] = useState(null);
  const isColorPickingRef = useRef(isColorPicking);

  // Thickness picker state
  const [isThicknessPicking, setIsThicknessPicking] = useState(false);
  const [thicknessPickerPos, setThicknessPickerPos] = useState(null);
  const [thicknessValue, setThicknessValue] = useState(DRAWING_CONFIG.DEFAULT_THICKNESS);
  const [thicknessSliderRelY, setThicknessSliderRelY] = useState(
    1 - (DRAWING_CONFIG.DEFAULT_THICKNESS - DRAWING_CONFIG.THICKNESS_MIN) /
    (DRAWING_CONFIG.THICKNESS_MAX - DRAWING_CONFIG.THICKNESS_MIN)
  );
  const isThicknessPickingRef = useRef(isThicknessPicking);

  // Debug state
  const [showDebugUI, setShowDebugUI] = useState(false);
  const showDebugUIRef = useRef(showDebugUI);

  // Word selection state
  const [selectedWord, setSelectedWord] = useState(null);
  const [isSelecting, setIsSelecting] = useState(false);
  const [selectionProgress, setSelectionProgress] = useState(0);
  const [wordPositions, setWordPositions] = useState([]);

  // Pinch visualization state
  const [pinchPosition, setPinchPosition] = useState(null);
  const [isPinching, setIsPinching] = useState(false);
  const selectionTimerRef = useRef(null);
  const progressIntervalRef = useRef(null);

  // Initialization tracking
  const isInitializedRef = useRef(false);

  // Refs for accessing current state in callbacks
  const isWordSelectionModeRef = useRef(isWordSelectionMode);
  const wordPositionsRef = useRef(wordPositions);
  const isSelectingRef = useRef(isSelecting);
  const selectedWordRef = useRef(selectedWord);
  const disabledRef = useRef(disabled);
  const onWordSelectedRef = useRef(onWordSelected);

  // ===== TRACKING REFS =====
  const lastGestureRef = useRef('Unknown');
  const drawingDotsRef = useRef([]);
  const latestLandmarksRef = useRef(null);
  const smoothedTipRef = useRef(null);
  const prevModeRef = useRef();
  const lastDrawPointTimeRef = useRef(0);

  // ===== PERFORMANCE OPTIMIZATION REFS =====
  const lastProcessTimeRef = useRef(0);
  const lastFaceProcessTimeRef = useRef(0);
  const landmarkCacheRef = useRef(null);
  const distanceCacheRef = useRef(new Map());

  // ===== HAND TRACKING REFS =====
  const handTrackingRef = useRef({
    lastValidHands: [],
    trackingHistory: [],
    confidenceThreshold: HAND_TRACKING_CONFIG.CONFIDENCE_THRESHOLD,
    maxHistorySize: HAND_TRACKING_CONFIG.MAX_HISTORY_SIZE,
    smoothedPositions: new Map(),
    positionSmoothingFactor: HAND_TRACKING_CONFIG.POSITION_SMOOTHING_FACTOR
  });

  const dominantHandRef = useRef(null);
  const handLockTimeoutRef = useRef(null);

  // ===== MODE TRACKING REFS =====
  const modeStabilityRef = useRef({
    currentMode: GESTURE_TYPES.NOTHING,
    stableMode: GESTURE_TYPES.NOTHING,
    startTime: performance.now(),
    consecutiveCount: 0
  });

  const movingModeRef = useRef({
    isActive: false,
    initialHandPosition: null,
    initialDrawingPositions: [],
    smoothedDelta: { x: 0, y: 0 }
  });

  const scalingModeRef = useRef({
    isActive: false,
    initialDistance: null,
    initialDrawingPositions: [],
    initialDrawingSizes: [],
    centerPoint: null
  });

  // ===== MEMOIZED VALUES =====
  const { width, height } = useMemo(() => ({
    width: UI_CONFIG.CANVAS_WIDTH,
    height: UI_CONFIG.CANVAS_HEIGHT
  }), []);

  const colorPickerConfig = useMemo(() => ({
    size: UI_CONFIG.COLOR_PICKER_SIZE,
    sliderWidth: UI_CONFIG.COLOR_PICKER_SLIDER_WIDTH,
    sliderHeight: UI_CONFIG.COLOR_PICKER_SIZE,
    margin: UI_CONFIG.COLOR_PICKER_MARGIN
  }), []);

  const thicknessConfig = useMemo(() => ({
    min: DRAWING_CONFIG.THICKNESS_MIN,
    max: DRAWING_CONFIG.THICKNESS_MAX,
    sliderHeight: UI_CONFIG.THICKNESS_SLIDER_HEIGHT,
    sliderWidth: UI_CONFIG.THICKNESS_SLIDER_WIDTH
  }), []);

  // ===== WORD SELECTION FUNCTIONS =====
  const calculateWordPositions = useCallback(() => {
    if (!isWordSelectionMode || wordOptions.length === 0) return [];

    const canvasWidth = width;
    const canvasHeight = height;
    const wordCount = wordOptions.length;
    const spacing = 30; // Space between words

    // Use uniform width for all words (same horizontal size)
    const uniformWidth = 180; // Fixed width for all words

    // Calculate total width needed
    const totalWidth = (uniformWidth * wordCount) + (spacing * (wordCount - 1));

    // Center the entire group
    const startX = (canvasWidth - totalWidth) / 2;
    const centerY = canvasHeight * 0.5; // Center vertically

    let currentX = startX;
    return wordOptions.map((word, index) => {
      const position = {
        word,
        x: currentX + (uniformWidth / 2),
        y: centerY,
        width: uniformWidth,
        height: 70 // Fixed height
      };
      currentX += uniformWidth + spacing;
      return position;
    });
  }, [isWordSelectionMode, wordOptions, width, height]);

  const checkPinchOnWord = useCallback((pinchX, pinchY, wordPositions) => {
    if (!isWordSelectionMode || !wordPositions.length) return null;

    for (const wordPos of wordPositions) {
      const isInside = pinchX >= (wordPos.x - wordPos.width / 2) &&
                      pinchX <= (wordPos.x + wordPos.width / 2) &&
                      pinchY >= (wordPos.y - wordPos.height / 2) &&
                      pinchY <= (wordPos.y + wordPos.height / 2);

      if (isInside) {
        return wordPos.word;
      }
    }
    return null;
  }, [isWordSelectionMode]);

  const handleWordSelectionStart = useCallback((word) => {
    if (!isWordSelectionMode || disabled || !onWordSelected) return;

    setSelectedWord(word);
    setIsSelecting(true);
    setSelectionProgress(0);

    // Clear any existing timers
    if (selectionTimerRef.current) {
      clearTimeout(selectionTimerRef.current);
    }
    if (progressIntervalRef.current) {
      clearInterval(progressIntervalRef.current);
    }

    // Start progress animation
    progressIntervalRef.current = setInterval(() => {
      setSelectionProgress(prev => {
        const newProgress = prev + (100 / 20); // 2 seconds = 20 intervals of 100ms
        return Math.min(newProgress, 100);
      });
    }, 100);

    // Set timer for 2 seconds
    selectionTimerRef.current = setTimeout(() => {
      setIsSelecting(false);
      setSelectionProgress(100);
      onWordSelected(word);

      // Clear timers
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
        progressIntervalRef.current = null;
      }
    }, 2000);
  }, [isWordSelectionMode, disabled, onWordSelected]);

  const handleWordSelectionEnd = useCallback(() => {
    if (!isWordSelectionMode) return;

    setIsSelecting(false);
    setSelectedWord(null);
    setSelectionProgress(0);

    // Clear timers
    if (selectionTimerRef.current) {
      clearTimeout(selectionTimerRef.current);
      selectionTimerRef.current = null;
    }
    if (progressIntervalRef.current) {
      clearInterval(progressIntervalRef.current);
      progressIntervalRef.current = null;
    }
  }, [isWordSelectionMode]);

  // Update word positions when word selection mode changes
  useEffect(() => {
    //console.log('🔄 WORD_POSITIONS_EFFECT: Running with isWordSelectionMode:', isWordSelectionMode);
    if (isWordSelectionMode) {
      const positions = calculateWordPositions();
      setWordPositions(positions);
      //console.log('🔄 WORD_POSITIONS_EFFECT: Set positions:', positions.length);
    } else {
      setWordPositions([]);
      // Clear any ongoing selection directly without dependency
      setIsSelecting(false);
      setSelectedWord(null);
      setSelectionProgress(0);
      if (selectionTimerRef.current) {
        clearTimeout(selectionTimerRef.current);
        selectionTimerRef.current = null;
      }
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
        progressIntervalRef.current = null;
      }
      //console.log('🔄 WORD_POSITIONS_EFFECT: Cleared positions');
    }
  }, [isWordSelectionMode, wordOptions]);

  // ===== UTILITY FUNCTIONS =====
  // Custom hook to check if pinch is on icons or words, using refs for latest values
  function useCheckPinchOnIcons() {
    return useCallback(() => {
      const landmarks = latestLandmarksRef.current ? latestLandmarksRef.current[0] : null;
      const headBar = headBarPosRef.current;
      if (!landmarks) return;

      const thumbTip = landmarks[4];
      const indexTip = landmarks[8];

      // Always use the original dimensions for coordinate calculations
      const x1 = (1 - thumbTip.x) * width;
      const y1 = thumbTip.y * height;
      const x2 = (1 - indexTip.x) * width;
      const y2 = indexTip.y * height;
      const pinchDist = Math.hypot(x1 - x2, y1 - y2);

      // Use original scale factor for pinch threshold
      const scaleFactor = Math.min(width / UI_CONFIG.CANVAS_WIDTH, height / UI_CONFIG.CANVAS_HEIGHT);
      const scaledPinchThreshold = 40 * scaleFactor; // Increased threshold for easier detection

      if (pinchDist > scaledPinchThreshold) {
        // If pinch is released, end word selection and hide pinch visualization
        setIsPinching(false);
        setPinchPosition(null);
        if (isWordSelectionMode && isSelecting) {
          handleWordSelectionEnd();
        }
        return;
      }

      const midX = (x1 + x2) / 2;
      const midY = (y1 + y2) / 2;

      // Update pinch visualization
      setIsPinching(true);
      setPinchPosition({ x: midX, y: midY });

      // Check for word selection first (higher priority in word selection mode)
      const currentIsWordSelectionMode = isWordSelectionModeRef.current;
      const currentWordPositions = wordPositionsRef.current;
      const currentIsSelecting = isSelectingRef.current;
      const currentSelectedWord = selectedWordRef.current;
      const currentDisabled = disabledRef.current;
      const currentOnWordSelected = onWordSelectedRef.current;

      if (currentIsWordSelectionMode && currentWordPositions.length > 0) {
        const selectedWordFromPinch = checkPinchOnWord(midX, midY, currentWordPositions);
        if (selectedWordFromPinch) {
          if (!currentIsSelecting || currentSelectedWord !== selectedWordFromPinch) {
            // Start new selection or switch to different word - call directly
            if (!currentIsWordSelectionMode || currentDisabled || !currentOnWordSelected) return;
            setSelectedWord(selectedWordFromPinch);
            setIsSelecting(true);
            setSelectionProgress(0);

            // Clear any existing timers
            if (selectionTimerRef.current) {
              clearTimeout(selectionTimerRef.current);
            }
            if (progressIntervalRef.current) {
              clearInterval(progressIntervalRef.current);
            }

            // Start progress animation
            progressIntervalRef.current = setInterval(() => {
              setSelectionProgress(prev => {
                const newProgress = prev + (100 / 20); // 2 seconds = 20 intervals of 100ms
                return Math.min(newProgress, 100);
              });
            }, 100);

            // Set timer for 2 seconds
            selectionTimerRef.current = setTimeout(() => {
              setIsSelecting(false);
              setSelectionProgress(100);
              currentOnWordSelected(selectedWordFromPinch);

              // Clear timers
              if (progressIntervalRef.current) {
                clearInterval(progressIntervalRef.current);
                progressIntervalRef.current = null;
              }
            }, 2000);
          }
          return; // Don't check icons when selecting words
        } else if (currentIsSelecting) {
          // Pinch moved away from word, cancel selection - call directly
          setIsSelecting(false);
          setSelectedWord(null);
          setSelectionProgress(0);

          // Clear timers
          if (selectionTimerRef.current) {
            clearTimeout(selectionTimerRef.current);
            selectionTimerRef.current = null;
          }
          if (progressIntervalRef.current) {
            clearInterval(progressIntervalRef.current);
            progressIntervalRef.current = null;
          }
          return;
        }
      }

      // Only check icons if not in word selection mode or no head bar
      if (!headBar || currentIsWordSelectionMode) return;

      const iconMidX = (x1 + x2) / 2;
      const iconMidY = (y1 + y2) / 2;

      // Add safety checks for canvas and DOM elements
      if (!canvasRef.current) return;

      // Get fresh DOM element references each time to handle dynamic scaling
      const colorWheelElement = document.querySelector('.color-wheel');
      const thicknessElement = document.querySelector('.thickness');

      if (!colorWheelElement || !thicknessElement) return;

      const canvasRect = canvasRef.current.getBoundingClientRect();
      const colorWheelRect = colorWheelElement.getBoundingClientRect();
      const thicknessRect = thicknessElement.getBoundingClientRect();

      // Calculate scale factors dynamically from actual DOM dimensions
      const actualScaleX = canvasRect.width / width;
      const actualScaleY = canvasRect.height / height;

      // Convert coordinates from original space to actual DOM space
      const domX = canvasRect.left + (iconMidX * actualScaleX);
      const domY = canvasRect.top + (iconMidY * actualScaleY);

      // Add some tolerance for better user experience
      const tolerance = 5;

      // Debug logging to help troubleshoot scaling issues
      if (showDebugUIRef.current) {
        /*console.log('Pinch Debug:', {
          pinchCoords: { domX: domX.toFixed(1), domY: domY.toFixed(1) },
          canvasScale: { scaleX: actualScaleX.toFixed(3), scaleY: actualScaleY.toFixed(3) },
          colorWheel: {
            left: colorWheelRect.left.toFixed(1),
            right: colorWheelRect.right.toFixed(1),
            top: colorWheelRect.top.toFixed(1),
            bottom: colorWheelRect.bottom.toFixed(1)
          },
          thickness: {
            left: thicknessRect.left.toFixed(1),
            right: thicknessRect.right.toFixed(1),
            top: thicknessRect.top.toFixed(1),
            bottom: thicknessRect.bottom.toFixed(1)
          }
        });*/
      }

      if (
        domX >= (colorWheelRect.left - tolerance) && domX <= (colorWheelRect.right + tolerance) &&
        domY >= (colorWheelRect.top - tolerance) && domY <= (colorWheelRect.bottom + tolerance)
      ) {
        setIsColorPicking(true);
        setIsThicknessPicking(false);
      } else if (
        domX >= (thicknessRect.left - tolerance) && domX <= (thicknessRect.right + tolerance) &&
        domY >= (thicknessRect.top - tolerance) && domY <= (thicknessRect.bottom + tolerance)
      ) {
        setIsThicknessPicking(true);
        setIsColorPicking(false);
      }
    }, [width, height]);
  }

  const checkPinchOnIcons = useCheckPinchOnIcons();

  useEffect(() => {
    //console.log('🚀 MAIN_INIT_EFFECT: Running, isInitialized:', isInitializedRef.current);
    /*console.log('🚀 MAIN_INIT_EFFECT: Current refs state:', {
      hands: !!handsRef.current,
      face: !!faceRef.current,
      camera: !!cameraRef.current,
      video: !!videoRef.current,
      canvas: !!canvasRef.current
    });*/

    // Initialize if not already initialized AND refs are available
    if (!isInitializedRef.current && videoRef.current && canvasRef.current) {
      //console.log('🚀 MAIN_INIT_EFFECT: Starting initialization...');
      isInitializedRef.current = true;
      initializeHandsAndFace();
    } else if (isInitializedRef.current) {
      //console.log('🚀 MAIN_INIT_EFFECT: Already initialized, skipping');
    } else {
      //console.log('🚀 MAIN_INIT_EFFECT: Refs not ready yet, waiting...');
    }

    return () => {
      //console.log('🧹 MAIN_INIT_EFFECT: Cleanup running');
      // Enhanced cleanup for better memory management
      if (handLandmarkerRef.current) {
        handLandmarkerRef.current.close();
      }
      if (faceDetectorRef.current) {
        faceDetectorRef.current.close();
      }
      // Stop and release webcam stream
      if (videoRef.current && videoRef.current.srcObject) {
        videoRef.current.srcObject.getTracks().forEach(track => track.stop());
        videoRef.current.srcObject = null;
      }
      // Clear performance optimization caches
      landmarkCacheRef.current = null;
      distanceCacheRef.current.clear();
      latestLandmarksRef.current = null;
      smoothedTipRef.current = null;

      // Clear dominant hand system
      dominantHandRef.current = null;
      if (handLockTimeoutRef.current) {
        clearTimeout(handLockTimeoutRef.current);
        handLockTimeoutRef.current = null;
      }

      // Clear mode stability tracking
      modeStabilityRef.current = {
        currentMode: 'Nothing',
        stableMode: 'Nothing',
        startTime: performance.now(),
        consecutiveCount: 0
      };

      // Clear moving mode tracking
      movingModeRef.current = {
        isActive: false,
        initialHandPosition: null,
        initialDrawingPositions: [],
        smoothedDelta: { x: 0, y: 0 }
      };

      // Clear scaling mode tracking
      scalingModeRef.current = {
        isActive: false,
        initialDistance: null,
        initialDrawingPositions: [],
        initialDrawingSizes: [],
        centerPoint: null
      };

      // Cancel any pending animation frames
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }

      // Clear word selection timers
      if (selectionTimerRef.current) {
        clearTimeout(selectionTimerRef.current);
        selectionTimerRef.current = null;
      }
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
        progressIntervalRef.current = null;
      }

      // Only reset initialization flag if we're actually cleaning up MediaPipe components
      if (handsRef.current || faceRef.current || cameraRef.current) {
        //console.log('🧹 MAIN_INIT_EFFECT: Resetting initialization flag after cleanup');
        isInitializedRef.current = false;
      }
    };
  }, []);

  useEffect(() => {
    prevModeRef.current = mode;
  }, [mode]);

  // Keep headBarPosRef in sync with headBarPos
  useEffect(() => {
    headBarPosRef.current = headBarPos;
  }, [headBarPos]);

  useEffect(() => {
    isColorPickingRef.current = isColorPicking;
  }, [isColorPicking]);

  useEffect(() => {
    isThicknessPickingRef.current = isThicknessPicking;
  }, [isThicknessPicking]);

  useEffect(() => { showDebugUIRef.current = showDebugUI; }, [showDebugUI]);

  // Keep refs in sync with state for callback access
  useEffect(() => { isWordSelectionModeRef.current = isWordSelectionMode; }, [isWordSelectionMode]);
  useEffect(() => { wordPositionsRef.current = wordPositions; }, [wordPositions]);
  useEffect(() => { isSelectingRef.current = isSelecting; }, [isSelecting]);
  useEffect(() => { selectedWordRef.current = selectedWord; }, [selectedWord]);
  useEffect(() => { disabledRef.current = disabled; }, [disabled]);
  useEffect(() => { onWordSelectedRef.current = onWordSelected; }, [onWordSelected]);

  // Drawing data transmission effect - optimized for real-time sync with normalized coordinates
  const lastDrawingDataRef = useRef([]);
  const transmissionCountRef = useRef(0);
  useEffect(() => {
    if (!onDrawingData || disabled || isWordSelectionMode) return;

    // Very frequent transmission for real-time drawing
    const throttleInterval = 30; // Send every 30ms for ultra-smooth updates
    const sendDrawingData = () => {
      // Get current canvas dimensions for normalization
      const canvas = canvasRef.current;
      if (!canvas) return;

      const canvasWidth = canvas.width;
      const canvasHeight = canvas.height;

      // Normalize coordinates to 0-1 range for cross-screen compatibility
      const currentDrawingData = drawingDotsRef.current.map(dot => ({
        // Normalize x,y coordinates to 0-1 range
        x: dot.x / width,
        y: dot.y / height,
        z: 0, // Normalize z as well for consistency
        color: dot.mesh?.material?.color?.getHexString() || 'ffffff',
        // Normalize thickness relative to canvas width for consistent appearance
        thickness: (dot.mesh?.geometry?.parameters?.radius * 2 || 4) / canvasWidth
      }));

      // Send normalized data with canvas dimensions for proper scaling
      const transmissionData = {
        points: currentDrawingData,
        canvasWidth: canvasWidth,
        canvasHeight: canvasHeight,
        timestamp: Date.now(),
        format: 'normalized' // Indicate this is the new normalized format
      };

      // Always send the complete array to ensure sync (including deletions)
      // This ensures other players always have the exact same drawing state
      onDrawingData(transmissionData);
      lastDrawingDataRef.current = [...currentDrawingData]; // Create a copy

      // Debug logging every 10 transmissions to monitor performance
      transmissionCountRef.current++;
      if (transmissionCountRef.current % 10 === 0) {
        //console.log('🎨 Drawing data transmitted:', currentDrawingData.length, 'normalized points (transmission #' + transmissionCountRef.current + ')');
      }
    };

    const interval = setInterval(sendDrawingData, throttleInterval);
    return () => clearInterval(interval);
  }, [onDrawingData, disabled, isWordSelectionMode]);

  const initializeHandsAndFace = async () => {
    //console.log('🎬 INIT_FUNCTION: Starting initializeHandsAndFace');
    try {
      // Prevent double initialization
      if (handLandmarkerRef.current || faceDetectorRef.current || visionRef.current) {
        /*console.log('🔄 INIT_FUNCTION: Already initialized, skipping...', {
          handLandmarker: !!handLandmarkerRef.current,
          faceDetector: !!faceDetectorRef.current,
          vision: !!visionRef.current
        });*/
        // Make sure loading state is cleared if already initialized
        if (isLoading) {
          setIsLoading(false);
        }
        return;
      }

      //console.log('🚀 INIT_FUNCTION: Starting fresh initialization...');
      const video = videoRef.current;
      const canvas = canvasRef.current;

      if (!video || !canvas) {
        console.error('❌ INIT_FUNCTION: Missing video or canvas refs', { video: !!video, canvas: !!canvas });
        setIsLoading(false);
        setError('Failed to initialize: Missing video or canvas elements');
        return;
      }

      const ctx = canvas.getContext('2d');

      // Set up webcam
      //console.log('📹 INIT_FUNCTION: Requesting camera access...');
      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          width: { ideal: 960 }, // 4:3, larger than 640
          height: { ideal: 720 },
          aspectRatio: 4/3,
          facingMode: 'user'
        }
      });
      //console.log('📹 INIT_FUNCTION: Camera access granted, setting up video...');
      video.srcObject = stream;
      video.onloadedmetadata = () => {
        //console.log('📹 INIT_FUNCTION: Video metadata loaded, starting playback...');
        video.play();
      };

      // Initialize MediaPipe Tasks Vision
      //console.log('🔧 INIT_FUNCTION: Initializing MediaPipe Tasks Vision...');
      visionRef.current = await FilesetResolver.forVisionTasks(
        "https://cdn.jsdelivr.net/npm/@mediapipe/tasks-vision@latest/wasm"
      );

      // Initialize HandLandmarker
      handLandmarkerRef.current = await HandLandmarker.createFromModelPath(
        visionRef.current,
        MEDIAPIPE_MODELS.HAND_LANDMARKER
      );
      await handLandmarkerRef.current.setOptions({
        numHands: 2,
        minHandDetectionConfidence: 0.6,
        minHandPresenceConfidence: 0.5,
        minTrackingConfidence: 0.5,
        runningMode: 'VIDEO'
      });
      //console.log('🤲 INIT_FUNCTION: HandLandmarker initialized');

      // Initialize FaceDetector
      faceDetectorRef.current = await FaceDetector.createFromModelPath(
        visionRef.current,
        MEDIAPIPE_MODELS.FACE_DETECTOR
      );
      await faceDetectorRef.current.setOptions({
        minDetectionConfidence: 0.3,
        minSuppressionThreshold: 0.3,
        runningMode: 'VIDEO'
      });
      //console.log('👤 INIT_FUNCTION: FaceDetector initialized');

      // Processing loop for Tasks-Vision
      const processFrame = async () => {
        if (!videoRef.current || !handLandmarkerRef.current || !faceDetectorRef.current) return;

        const startTimeMs = performance.now();

        // Clear canvas and draw video frame
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.drawImage(videoRef.current, 0, 0, canvas.width, canvas.height);

        // Process hand landmarks
        const handResults = handLandmarkerRef.current.detectForVideo(videoRef.current, startTimeMs);

        // Process face detection
        const faceResults = faceDetectorRef.current.detectForVideo(videoRef.current, startTimeMs);

        // Convert hand results to legacy format for compatibility
        const legacyHandResults = {
          multiHandLandmarks: handResults.landmarks || [],
          multiHandedness: handResults.handednesses || [],
          image: videoRef.current
        };

        // Process hand results using existing logic
        processHandResults(legacyHandResults, ctx, canvas);

        // Process face results
        processFaceResults(faceResults, canvas);

        // Continue animation loop
        requestAnimationFrame(processFrame);
      };

      // Extract hand processing logic into separate function
      const processHandResults = (results, ctx, canvas) => {
        // If disabled, only show video feed without drawing/erasing gesture processing
        // But still allow hand tracking for word selection and pinch detection
        if (disabled && !isWordSelectionMode) {
          return;
        }

        let detectedGestures = [];
        let detectedHandInfo = [];
        // Optimized duplicate hand filtering with efficient distance calculations
        let filteredLandmarks = [];
        let filteredHandedness = [];
        let filteredConfidences = [];
        if (results.multiHandLandmarks && results.multiHandedness) {
          // Create array of hand data with confidence scores for sorting
          const handData = results.multiHandLandmarks.map((landmarks, i) => ({
            landmarks,
            handedness: results.multiHandedness[i],
            confidence: results.multiHandedness[i].score || 0.5,
            wrist: landmarks[0]
          }));

          // Filter out low confidence detections for better reliability
          const highConfidenceHands = handData.filter(hand =>
            hand.confidence >= handTrackingRef.current.confidenceThreshold
          );

          // Sort by confidence (highest first) to prioritize better detections
          const sortedHandData = highConfidenceHands.length > 0 ? highConfidenceHands : handData;
          sortedHandData.sort((a, b) => b.confidence - a.confidence);

          // Update tracking history for hand continuity
          handTrackingRef.current.trackingHistory.push({
            timestamp: performance.now(),
            handCount: sortedHandData.length,
            hands: sortedHandData.map(hand => ({
              handedness: hand.handedness.label,
              confidence: hand.confidence,
              wristPos: { x: hand.wrist.x, y: hand.wrist.y }
            }))
          });

          // Keep only recent history
          if (handTrackingRef.current.trackingHistory.length > handTrackingRef.current.maxHistorySize) {
            handTrackingRef.current.trackingHistory.shift();
          }

          // Enhanced hand detection: One hand per handedness type, allow second hand when first is pinching
          let dominantHandGesture = null;
          let detectedHandedness = new Set(); // Track which handedness types are already detected

          // First pass: Process dominant hand and detect its gesture
          for (const hand of sortedHandData) {
            const wrist = hand.wrist;

            // Check if this is a duplicate of already filtered hands
            const isDuplicate = filteredLandmarks.some(existing => {
              const dx = existing[0].x - wrist.x;
              const dy = existing[0].y - wrist.y;
              return (dx*dx + dy*dy) < 0.008; // 0.08^2 for tighter threshold
            });

            // Check if we already have a hand of this handedness type
            const handednessAlreadyDetected = detectedHandedness.has(hand.handedness.label);

            if (!isDuplicate && !handednessAlreadyDetected) {
              // Apply position smoothing for more stable tracking
              const handId = hand.handedness.label;
              const smoothedLandmarks = smoothHandPosition(hand.landmarks, handId);

              // Set dominant hand if not set (first hand becomes dominant)
              if (!dominantHandRef.current) {
                dominantHandRef.current = {
                  handedness: hand.handedness.label,
                  wristPosition: { x: smoothedLandmarks[0].x, y: smoothedLandmarks[0].y },
                  timestamp: performance.now()
                };
                //console.log(`Dominant hand locked: ${dominantHandRef.current.handedness}`);
              }

              // Check if this hand matches the dominant hand (use smoothed position)
              const smoothedWrist = smoothedLandmarks[0];
              const isDominantHand = dominantHandRef.current &&
                hand.handedness.label === dominantHandRef.current.handedness &&
                Math.abs(smoothedWrist.x - dominantHandRef.current.wristPosition.x) < 0.3 &&
                Math.abs(smoothedWrist.y - dominantHandRef.current.wristPosition.y) < 0.3;

              if (isDominantHand) {
                // This is the dominant hand - always add it (use smoothed landmarks)
                filteredLandmarks.push(smoothedLandmarks);
                filteredHandedness.push(hand.handedness);
                filteredConfidences.push(hand.confidence);
                detectedHandedness.add(hand.handedness.label);

                // Update dominant hand position with smoothed position
                dominantHandRef.current.wristPosition = { x: smoothedWrist.x, y: smoothedWrist.y };
                dominantHandRef.current.timestamp = performance.now();

                // Detect gesture of dominant hand to check if it's pinching
                const handedness = hand.handedness.label === 'Left' ? 'Right' : 'Left';
                const orientation = getOrientation(smoothedLandmarks);
                dominantHandGesture = detectGesture(smoothedLandmarks, handedness, orientation);
                //console.log(`Dominant hand gesture: ${dominantHandGesture}`);
              }
            } else if (handednessAlreadyDetected) {
              //console.log(`Duplicate handedness ignored: ${hand.handedness.label} (Already have a ${hand.handedness.label} hand)`);
            }
          }

          // Second pass: Process secondary hands only if dominant hand is pinching and different handedness
          for (const hand of sortedHandData) {
            const wrist = hand.wrist;

            // Check if this is a duplicate of already filtered hands
            const isDuplicate = filteredLandmarks.some(existing => {
              const dx = existing[0].x - wrist.x;
              const dy = existing[0].y - wrist.y;
              return (dx*dx + dy*dy) < 0.008; // 0.08^2 for tighter threshold
            });

            // Check if we already have a hand of this handedness type
            const handednessAlreadyDetected = detectedHandedness.has(hand.handedness.label);

            if (!isDuplicate && !handednessAlreadyDetected) {
              // Check if this hand matches the dominant hand
              const isDominantHand = dominantHandRef.current &&
                hand.handedness.label === dominantHandRef.current.handedness &&
                Math.abs(wrist.x - dominantHandRef.current.wristPosition.x) < 0.3 &&
                Math.abs(wrist.y - dominantHandRef.current.wristPosition.y) < 0.3;

              if (!isDominantHand) {
                // This is a secondary hand with different handedness - only add if dominant hand is pinching
                if (dominantHandGesture === 'Pinch') {
                  //console.log(`Secondary hand allowed: ${hand.handedness.label} (Dominant hand is pinching, different handedness)`);
                  // Apply smoothing to secondary hand as well
                  const secondaryHandId = hand.handedness.label + '_secondary';
                  const smoothedSecondaryLandmarks = smoothHandPosition(hand.landmarks, secondaryHandId);
                  filteredLandmarks.push(smoothedSecondaryLandmarks);
                  filteredHandedness.push(hand.handedness);
                  filteredConfidences.push(hand.confidence);
                  detectedHandedness.add(hand.handedness.label);
                } else {
                  //console.log(`Secondary hand ignored: ${hand.handedness.label} (Dominant hand gesture: ${dominantHandGesture || 'not pinching'})`);
                }
              }
            } else if (handednessAlreadyDetected) {
              //console.log(`Duplicate handedness ignored: ${hand.handedness.label} (Already have a ${hand.handedness.label} hand)`);
            }
          }
          // Process all detected hands and apply smart filtering
          let allDetectedGestures = [];
          let allHandInfo = [];

          for (let i = 0; i < filteredLandmarks.length; i++) {
            const landmarks = filteredLandmarks[i];
            let handedness = filteredHandedness[i].label;
            handedness = handedness === 'Left' ? 'Right' : 'Left';
            const orientation = getOrientation(landmarks);

            if (showDebugUIRef.current) {
              drawHandLandmarks(ctx, landmarks);
            }

            const gesture = detectGesture(landmarks, handedness, orientation);

            // Store all gestures and hand info
            allDetectedGestures.push(gesture);
            allHandInfo.push({
              handedness,
              orientation,
              confidence: filteredConfidences[i] || 1.0,
              isDominant: dominantHandRef.current && handedness === dominantHandRef.current.handedness,
            });


          }

          // Use detected gestures directly (only dominant hand is in the arrays)
          detectedGestures = allDetectedGestures;
          detectedHandInfo = allHandInfo;
        }
        // Batch state updates to reduce re-renders
        if (JSON.stringify(detectedGestures) !== JSON.stringify(currentGestures)) {
          setCurrentGestures(detectedGestures);
        }
        if (JSON.stringify(detectedHandInfo) !== JSON.stringify(handInfo)) {
          setHandInfo(detectedHandInfo);
        }
        updateMode(detectedGestures);
        if (isLoading) {
          setIsLoading(false);
        }

        // Cache filtered landmarks for other components to use
        if (filteredLandmarks.length > 0) {
          landmarkCacheRef.current = filteredLandmarks;
          latestLandmarksRef.current = filteredLandmarks;

          // Clear any existing timeout since we have the dominant hand
          if (handLockTimeoutRef.current) {
            clearTimeout(handLockTimeoutRef.current);
            handLockTimeoutRef.current = null;
          }
        } else {
          landmarkCacheRef.current = null;
          latestLandmarksRef.current = null;

          // Start timeout to reset dominant hand if no hands detected for 0.5 second
          if (dominantHandRef.current && !handLockTimeoutRef.current) {
            handLockTimeoutRef.current = setTimeout(() => {
              dominantHandRef.current = null;
              handLockTimeoutRef.current = null;

              // Clear smoothed positions for lost hands
              handTrackingRef.current.smoothedPositions.clear();

              // Clear mode stability for the removed hand
              modeStabilityRef.current = {
                currentMode: 'Nothing',
                stableMode: 'Nothing',
                startTime: performance.now(),
                consecutiveCount: 0
              };
            }, PERFORMANCE_CONFIG.HAND_LOCK_TIMEOUT);
          }
        }
      };

      // Extract face processing logic into separate function
      const processFaceResults = (results, canvas) => {
        const alpha = 0.85; // Reduced smoothing for more responsive tracking (0 = very smooth, 1 = no smoothing)
        if (results.detections && results.detections.length > 0) {
          const face = results.detections[0];
          const box = face.boundingBox;
          const x = Math.max(16, Math.min(canvas.width - 16, (box.xCenter) * canvas.width));
          const y = Math.max(16, Math.min(canvas.height - 32, (box.yCenter * canvas.height) - (box.height * canvas.height) / 2 - 32)) - 50;

          if (smoothedheadBarPos.current) {
            // Simplified smoothing for better responsiveness
            const newX = alpha * smoothedheadBarPos.current.x + (1 - alpha) * x;
            const newY = alpha * smoothedheadBarPos.current.y + (1 - alpha) * y;

            smoothedheadBarPos.current = { x: newX, y: newY };

            // Update state more frequently for smoother tracking
            setheadBarPos({ x: newX, y: newY });
          } else {
            smoothedheadBarPos.current = { x, y };
            setheadBarPos({ x, y });
          }
        } else {
          setheadBarPos(null);
          smoothedheadBarPos.current = null;
        }
      };

      // Start processing loop
      processFrame();

      //console.log('✅ INIT_FUNCTION: Initialization complete!');
      setIsLoading(false);
    } catch (error) {
      console.error('❌ INIT_FUNCTION: Error during initialization:', error);
      setError('Could not access the camera. Please allow camera access and reload the page.');
      setIsLoading(false);
    }
  };


  const drawHandLandmarks = (ctx, landmarks) => {
    // Simple landmark drawing without MediaPipe drawing utils
    ctx.fillStyle = '#00FF00';
    ctx.strokeStyle = '#00FF00';
    ctx.lineWidth = 2;
    landmarks.forEach(point => {
      ctx.beginPath();
      ctx.arc(
        point.x * canvasRef.current.width,
        point.y * canvasRef.current.height,
        6,
        0,
        2 * Math.PI
      );
      ctx.fillStyle = 'rgba(0, 255, 0, 0.3)';
      ctx.fill();
      ctx.beginPath();
      ctx.arc(
        point.x * canvasRef.current.width,
        point.y * canvasRef.current.height,
        3,
        0,
        2 * Math.PI
      );
      ctx.fillStyle = '#00FF00';
      ctx.fill();
    });
  };

  // Get orientation (Up, Right, Down, Left) based on middle finger direction
  function getOrientation(landmarks) {
    // Use base and tip of middle finger
    const base = landmarks[0]; // MCP
    const tip = landmarks[10]; // Tip
    const dx = tip.x - base.x;
    const dy = tip.y - base.y;
    if (Math.abs(dx) > Math.abs(dy)) {
      // Horizontal
      return dx > 0 ? 'Left' : 'Right';
    } else {
      // Vertical
      return dy > 0 ? 'Down' : 'Up';
    }
  }

  // Hand position smoothing function for more stable tracking
  const smoothHandPosition = (landmarks, handId) => {
    const smoothing = handTrackingRef.current;
    const factor = smoothing.positionSmoothingFactor;

    if (!smoothing.smoothedPositions.has(handId)) {
      // First time seeing this hand - initialize with current position
      smoothing.smoothedPositions.set(handId, landmarks.map(point => ({ ...point })));
      return landmarks;
    }

    const smoothedLandmarks = smoothing.smoothedPositions.get(handId);

    // Apply exponential smoothing to each landmark point
    const newSmoothedLandmarks = landmarks.map((point, index) => {
      const smoothed = smoothedLandmarks[index];
      return {
        x: smoothed.x + (point.x - smoothed.x) * factor,
        y: smoothed.y + (point.y - smoothed.y) * factor,
        z: smoothed.z + (point.z - smoothed.z) * factor
      };
    });

    // Update stored smoothed positions
    smoothing.smoothedPositions.set(handId, newSmoothedLandmarks);

    return newSmoothedLandmarks;
  };

  const detectGesture = (landmarks, handedness, orientation) => {
    const thumbExtended = isFingerExtended(landmarks, 0, handedness, orientation);
    const indexExtended = isFingerExtended(landmarks, 1, handedness, orientation);
    const middleExtended = isFingerExtended(landmarks, 2, handedness, orientation);
    const ringExtended = isFingerExtended(landmarks, 3, handedness, orientation);
    const pinkyExtended = isFingerExtended(landmarks, 4, handedness, orientation);
    const extendedFingers = [
      thumbExtended,
      indexExtended,
      middleExtended,
      ringExtended,
      pinkyExtended
    ];
    let gesture = 'Unknown';

    // Enhanced drawing gesture detection with more stability
    const isDrawingGesture = !thumbExtended && indexExtended && !middleExtended && !ringExtended && !pinkyExtended;

    // Add additional checks for drawing gesture stability
    if (isDrawingGesture) {
      // Check if index finger is clearly extended and others are clearly not
      const indexTip = landmarks[8];
      const indexMcp = landmarks[5];
      const middleTip = landmarks[12];
      const middleMcp = landmarks[9];
      const ringTip = landmarks[16];
      const ringMcp = landmarks[13];
      const pinkyTip = landmarks[20];
      const pinkyMcp = landmarks[17];

      // Calculate finger extension ratios for more stable detection
      const indexExtensionRatio = Math.abs(indexTip.y - indexMcp.y);
      const middleExtensionRatio = Math.abs(middleTip.y - middleMcp.y);
      const ringExtensionRatio = Math.abs(ringTip.y - ringMcp.y);
      const pinkyExtensionRatio = Math.abs(pinkyTip.y - pinkyMcp.y);

      // Index should be significantly more extended than others
      const isStableDrawing = indexExtensionRatio > (middleExtensionRatio * 1.5) &&
                             indexExtensionRatio > (ringExtensionRatio * 1.5) &&
                             indexExtensionRatio > (pinkyExtensionRatio * 1.5);

      if (isStableDrawing) {
        gesture = 'Drawing';
      }
    }
    // Use enhanced gesture detection logic
    if (gesture === 'Drawing') {
      // Already detected as stable drawing gesture above
      return gesture;
    } else if (extendedFingers.every(finger => finger)) {
      gesture = 'Nothing';
    } else if (extendedFingers.every(finger => !finger)) {
      gesture = 'Moving';
    } else if (!thumbExtended && indexExtended && !middleExtended && !ringExtended && !pinkyExtended) {
      // Fallback to basic drawing detection if enhanced detection didn't catch it
      gesture = 'Drawing';
    } else if (!thumbExtended && indexExtended && middleExtended && !ringExtended && !pinkyExtended) {
      gesture = 'Erasing';
    }else if (thumbExtended && indexExtended && !middleExtended && !ringExtended && !pinkyExtended) {
      gesture = 'Select';
    }
    else if (thumbExtended && !indexExtended && !middleExtended && !ringExtended && !pinkyExtended && orientation === 'Up' ) {
      // Access current values from refs
      const currentIsColorPicking = isColorPickingRef.current;
      const currentIsThicknessPicking = isThicknessPickingRef.current;

      if(!currentIsColorPicking && !currentIsThicknessPicking){
        const indexSecondPoint = landmarks[7];
        const thumbSecondPoint = landmarks[2];
        const a1 = (1 - thumbSecondPoint.x) * width;
        const b1 = thumbSecondPoint.y * height;
        const a2 = (1 - indexSecondPoint.x) * width;
        const b2 = indexSecondPoint.y * height;
        // Pinch distance
        const fingerDist = Math.hypot(a1 - a2, b1 - b2);
        //console.log(fingerDist);
        if(fingerDist < 60){
          gesture = 'Moving';
          return gesture;
        }
      }
      const thumbTip = landmarks[4];
      const indexTip = landmarks[8];
      // Convert normalized coords to px
      const x1 = (1 - thumbTip.x) * width;
      const y1 = thumbTip.y * height;
      const x2 = (1 - indexTip.x) * width;
      const y2 = indexTip.y * height;
      // Pinch distance
      const pinchDist = Math.hypot(x1 - x2, y1 - y2);
      if (pinchDist < 65){
        gesture = 'Select';
      }
      else{
        gesture = 'Back';
      }
    }
    gesture = gesture !== 'Moving' && isPinchGesture(landmarks, handedness, orientation) ? 'Pinch' : gesture;
    if (gesture !== 'Unknown') {
      lastGestureRef.current = gesture;
      return gesture;
    } else {
      return lastGestureRef.current;
    }
  };

  // Use handedness and orientation for thumb logic if needed
  const isFingerExtended = (landmarks, fingerIndex, handedness, orientation) => {
    const fingerJoints = getFingerJoints(fingerIndex);  
    let middleJoint;
    let tipJoint;
    if(fingerIndex === 0){
      middleJoint = landmarks[fingerJoints[2]];
      tipJoint = landmarks[fingerJoints[4]];
      switch(orientation){
        case 'Up':
          if (handedness === "Right") {
            return tipJoint.x > middleJoint.x;
          } else {
            return tipJoint.x < middleJoint.x;
          }
        case 'Down':
          if (handedness === "Right") {
            return tipJoint.x > middleJoint.x;
          } else {
            return tipJoint.x < middleJoint.x;
          }
        case 'Right':
          if (handedness === "Right") {
            return tipJoint.y < middleJoint.y;
          } else {
          return tipJoint.y > middleJoint.y;
          }
        case 'Left':
          if (handedness === "Right") {
            return tipJoint.y > middleJoint.y;
          } else {
            return tipJoint.y < middleJoint.y;
          }
      }  
    }
    else{
      middleJoint = landmarks[fingerJoints[2]];
      tipJoint = landmarks[fingerJoints[4]];
      switch(orientation){
        case 'Up':
          return tipJoint.y < middleJoint.y;
        case 'Down':
          return tipJoint.y > middleJoint.y;
        case 'Right':
          return tipJoint.x < middleJoint.x;
        case 'Left':
          return tipJoint.x > middleJoint.x;
      }
    }
  };    

  const getFingerJoints = (fingerIndex) => {
    const fingerJoints = [
      [0, 1, 2, 3, 4],     // Thumb
      [0, 5, 6, 7, 8],     // Index
      [0, 9, 10, 11, 12],  // Middle
      [0, 13, 14, 15, 16], // Ring
      [0, 17, 18, 19, 20]  // Pinky
    ];
    return fingerJoints[fingerIndex];
  };

  const isPinchGesture = (landmarks, handedness, orientation) => {
    const thumbTip = landmarks[4];  // Thumb tip
    const indexTip = landmarks[8];  // Index finger tip
    const middleTip = landmarks[12]; // Middle finger tip

    // Use squared distances to avoid expensive sqrt calculations
    const pinchThresholdSq = 0.0042; // Increased for easier pinch

    // Calculate squared 3D distances between fingertips
    const dx1 = thumbTip.x - indexTip.x;
    const dy1 = thumbTip.y - indexTip.y;
    const dz1 = thumbTip.z - indexTip.z;
    const thumbIndexDistanceSq = dx1*dx1 + dy1*dy1 + dz1*dz1;

    const dx2 = thumbTip.x - middleTip.x;
    const dy2 = thumbTip.y - middleTip.y;
    const dz2 = thumbTip.z - middleTip.z;
    const thumbMiddleDistanceSq = dx2*dx2 + dy2*dy2 + dz2*dz2;

    const dx3 = indexTip.x - middleTip.x;
    const dy3 = indexTip.y - middleTip.y;
    const dz3 = indexTip.z - middleTip.z;
    const indexMiddleDistanceSq = dx3*dx3 + dy3*dy3 + dz3*dz3;

    // Check if all three fingers are close together using squared distances
    const isPinched = thumbIndexDistanceSq < pinchThresholdSq &&
                     thumbMiddleDistanceSq < pinchThresholdSq &&
                     indexMiddleDistanceSq < pinchThresholdSq;

    return isPinched;
  }

  const updateMode = (gestures) => {
    // ===== GESTURE STABILITY LOGIC =====
    // Apply stability to raw gestures first to prevent flickering
    const currentTime = performance.now();
    const stability = modeStabilityRef.current;

    // Determine raw intended mode based on current gestures
    let rawIntendedMode = 'Nothing';

    // In word selection mode, only allow Nothing and Pinch modes (for word selection)
    // Prevent drawing, erasing, moving, etc.
    const isInWordSelectionMode = isWordSelectionModeRef.current;

    if(isColorPickingRef.current){
      if(gestures[0] === 'Back'){
        rawIntendedMode = 'Back';
      } else {
        rawIntendedMode = 'Select';
      }
    }
    else if(isThicknessPickingRef.current){
      if(gestures[0] === 'Back'){
        rawIntendedMode = 'Back';
      } else {
        rawIntendedMode = 'Select';
      }
    }
    else if (gestures.length === 0) {
      // Even with no gestures, check for pinch on icons if we have landmarks
      const landmarks = latestLandmarksRef.current ? latestLandmarksRef.current[0] : null;
      if (landmarks) {
        checkPinchOnIcons();
      }
      rawIntendedMode = 'Nothing';
    } else if (gestures.length === 1) {
      // Always check for pinch on icons when we have hand landmarks, regardless of gesture
      const landmarks = latestLandmarksRef.current ? latestLandmarksRef.current[0] : null;
      if (landmarks) {
        checkPinchOnIcons();
      }

      if(gestures[0] === 'Pinch'){
        rawIntendedMode = 'Moving';
      }
      else if(gestures[0] === 'Select'){
        if(isColorPickingRef.current){
          rawIntendedMode = 'Select';
        }
        else{
          if(landmarks === null){
            rawIntendedMode = "Nothing";
          } else {
            const thumbTip = landmarks[4];
            const indexTip = landmarks[8];
            const x1 = (1 - thumbTip.x) * width;
            const y1 = thumbTip.y * height;
            const x2 = (1 - indexTip.x) * width;
            const y2 = indexTip.y * height;
            const pinchDist = Math.hypot(x1 - x2, y1 - y2);
            // Check if pinch is inside hue/sat square
            if (pinchDist > 60) {
              rawIntendedMode = "Drawing";
            }
            else{
              rawIntendedMode = "Select";
            }
          }
        }
      }
      else {
        rawIntendedMode = gestures[0];
      }
    } else {
      // Multiple hands detected - both hands are pinching
      if (gestures.every(gesture => gesture === 'Pinch')) {
        //console.log('Dual hand pinch mode activated');
        rawIntendedMode = 'Scaling';
      } else {
        // Mixed gestures - use dominant hand (first gesture)
        //console.log(`Mixed gestures detected: [${gestures.join(', ')}] - using dominant hand: ${gestures[0]}`);
        rawIntendedMode = gestures[0];
      }
    }

    // Filter out drawing/erasing modes when in word selection mode
    if (isInWordSelectionMode) {
      if (rawIntendedMode === 'Drawing' || rawIntendedMode === 'Erasing' || rawIntendedMode === 'Moving' || rawIntendedMode === 'Scaling') {
        //console.log('🚫 WORD_SELECTION: Blocking', rawIntendedMode, 'mode during word selection');
        rawIntendedMode = 'Nothing'; // Force to Nothing mode
      } else {
        //console.log('✅ WORD_SELECTION: Allowing', rawIntendedMode, 'mode during word selection');
      }
    }

    // ===== STABILITY FILTERING =====
    // Only change mode if the new mode has been consistent for the required time

    if (rawIntendedMode === stability.currentMode) {
      // Same mode as before - increment count and check if stable
      stability.consecutiveCount++;
      const timeHeld = currentTime - stability.startTime;

      // Different stability requirements based on mode
      let requiredStabilityTime = PERFORMANCE_CONFIG.MODE_STABILITY_TIME;
      if (rawIntendedMode === GESTURE_TYPES.MOVING) {
        requiredStabilityTime = 500; // 500ms for Moving mode
      } else if (rawIntendedMode === GESTURE_TYPES.DRAWING) {
        requiredStabilityTime = 200; // Faster response for Drawing mode
      }

      // Check if mode is stable (held for required time)
      const isStable = timeHeld >= requiredStabilityTime;

      if (isStable && stability.stableMode !== rawIntendedMode) {
        // Mode has been stable long enough - switch to it
        const previousMode = stability.stableMode;
        stability.stableMode = rawIntendedMode;
        setMode(rawIntendedMode);

        // Handle Moving mode transitions
        handleMovingModeTransition(previousMode, rawIntendedMode);
      } else if (stability.stableMode === rawIntendedMode) {
        // Already stable and same mode - just continue
        setMode(rawIntendedMode);
      } else {
        // Not stable yet - keep current stable mode
        setMode(stability.stableMode);
      }
    } else {
      // Different mode detected - reset stability tracking
      stability.currentMode = rawIntendedMode;
      stability.startTime = currentTime;
      stability.consecutiveCount = 1;

      // Keep current stable mode until new one is confirmed stable
      setMode(stability.stableMode);

    }
  };

  // Handle Moving mode transitions
  const handleMovingModeTransition = (previousMode, newMode) => {
    if (newMode === 'Moving' && previousMode !== 'Moving') {
      // Entering Moving mode - capture initial state
      const landmarks = latestLandmarksRef.current ? latestLandmarksRef.current[0] : null;
      if (landmarks) {
        // Get initial hand position (using palm)
        const indexTip = landmarks[0];
        const initialHandPos = {
          x: ((1 - indexTip.x) * width) - 30,
          y: ((1 - indexTip.y) * height) + 40  
        };

        // Store initial positions of all drawing dots
        const initialPositions = drawingDotsRef.current.map(dot => ({
          x: dot.x,
          y: dot.y,
          z: dot.z
        }));

        movingModeRef.current = {
          isActive: true,
          initialHandPosition: initialHandPos,
          initialDrawingPositions: initialPositions,
          smoothedDelta: { x: 0, y: 0 }
        };

        //console.log(`Moving mode started - Initial hand position: (${initialHandPos.x.toFixed(1)}, ${initialHandPos.y.toFixed(1)})`);
      }
    } else if (previousMode === 'Moving' && newMode !== 'Moving') {
      // Exiting Moving mode
      movingModeRef.current.isActive = false;
      //console.log('Moving mode ended');
    }

    // Scaling mode transitions
    if (newMode === 'Scaling' && previousMode !== 'Scaling') {
      // Entering Scaling mode - capture initial state with both hands
      const landmarks = latestLandmarksRef.current;
      if (landmarks && landmarks.length >= 2) {
        // Get positions of both hands (using index finger tips)
        const hand1IndexTip = landmarks[0][8];
        const hand2IndexTip = landmarks[1][8];

        const hand1Pos = {
          x: (1 - hand1IndexTip.x) * width,
          y: (1 - hand1IndexTip.y) * height
        };
        const hand2Pos = {
          x: (1 - hand2IndexTip.x) * width,
          y: (1 - hand2IndexTip.y) * height
        };

        // Calculate initial distance between hands
        const initialDistance = Math.hypot(hand2Pos.x - hand1Pos.x, hand2Pos.y - hand1Pos.y);

        // Calculate center point between hands
        const centerPoint = {
          x: (hand1Pos.x + hand2Pos.x) / 2,
          y: (hand1Pos.y + hand2Pos.y) / 2
        };

        // Store initial positions and sizes of all drawing dots
        const initialPositions = drawingDotsRef.current.map(dot => ({
          x: dot.x,
          y: dot.y,
          z: dot.z
        }));

        const initialSizes = drawingDotsRef.current.map(dot => {
          if (dot.mesh && dot.mesh.geometry) {
            return dot.mesh.geometry.parameters.radius;
          }
          return thicknessValue / 2; // Default radius
        });

        scalingModeRef.current = {
          isActive: true,
          initialDistance: initialDistance,
          initialDrawingPositions: initialPositions,
          initialDrawingSizes: initialSizes,
          centerPoint: centerPoint
        };

        //console.log(`Scaling mode started - Initial distance: ${initialDistance.toFixed(1)}px, Center: (${centerPoint.x.toFixed(1)}, ${centerPoint.y.toFixed(1)})`);
      }
    } else if (previousMode === 'Scaling' && newMode !== 'Scaling') {
      // Exiting Scaling mode
      scalingModeRef.current.isActive = false;
      //console.log('Scaling mode ended');
    }
  };

  // Compute gesture status for the options list
  const gestureStatus = GESTURE_OPTIONS.map(option =>
    currentGestures.includes(option)
  );

  // Always render the settings icon at the latest detected face position, mirrored horizontally
  const getHeadBarPosition = () => {
    // Reduce the horizontal offset for better alignment at higher res
    return {
      left: `calc(100% - ${headBarPos.x + 230}px)`,
      top: headBarPos.y + 40 , 
    };
  };

  // Setup Three.js overlay
  useEffect(() => {
    // Setup Three.js scene, camera, renderer
    const scene = new THREE.Scene();
    const camera = new THREE.OrthographicCamera(0, width, height, 0, -10, 10);
    const renderer = new THREE.WebGLRenderer({ canvas: threeCanvasRef.current, alpha: true });
    renderer.setClearColor(0x000000, 0); // transparent
    renderer.setSize(width, height);
    threeSceneRef.current = scene;
    threeCameraRef.current = camera;
    threeRendererRef.current = renderer;
    // Animation loop
    const animate = () => {
      renderer.render(scene, camera);
      animationFrameRef.current = requestAnimationFrame(animate);
    };
    animate();
    return () => {
      cancelAnimationFrame(animationFrameRef.current);
      renderer.dispose();
      // Dispose all dot meshes
      drawingDotsRef.current.forEach(dot => {
        if (dot.mesh) {
          scene.remove(dot.mesh);
          dot.mesh.geometry.dispose();
          dot.mesh.material.dispose();
        }
      });
      drawingDotsRef.current = [];
    };
  }, []);

  // Drawing logic: update dots when mode is Drawing
  useEffect(() => {
    // Moving mode: translate all drawing dots based on hand movement
    if (mode === 'Moving' && movingModeRef.current.isActive) {
      if (handInfo.length > 0) {
        const landmarks = latestLandmarksRef.current ? latestLandmarksRef.current[0] : null;
        if (landmarks && movingModeRef.current.initialHandPosition) {
          // Get current hand position
          const indexTip = landmarks[8];
          const currentHandPos = {
            x: (1 - indexTip.x) * width,
            y: (1 - indexTip.y) * height  // Fixed: invert Y to match screen coordinates
          };

          // Calculate raw movement delta
          const rawDeltaX = currentHandPos.x - movingModeRef.current.initialHandPosition.x;
          const rawDeltaY = currentHandPos.y - movingModeRef.current.initialHandPosition.y;

          // Apply smoothing to the delta values
          const smoothing = movingModeRef.current.smoothedDelta;
          smoothing.x = smoothing.x + (rawDeltaX - smoothing.x) * HAND_TRACKING_CONFIG.MOVING_SMOOTHING_FACTOR;
          smoothing.y = smoothing.y + (rawDeltaY - smoothing.y) * HAND_TRACKING_CONFIG.MOVING_SMOOTHING_FACTOR;

          // Apply smoothed translation to all drawing dots
          drawingDotsRef.current.forEach((dot, index) => {
            if (movingModeRef.current.initialDrawingPositions[index]) {
              const initialPos = movingModeRef.current.initialDrawingPositions[index];
              const newX = initialPos.x + smoothing.x;
              const newY = initialPos.y + smoothing.y;

              // Update dot position
              dot.x = newX;
              dot.y = newY;

              // Update mesh position in Three.js scene
              if (dot.mesh) {
                dot.mesh.position.set(newX, newY, 0);
              }
            }
          });
        }
      }
    }

    // Scaling mode: scale all drawing dots based on hand distance changes
    if (mode === 'Scaling' && scalingModeRef.current.isActive) {
      if (handInfo.length >= 2) {
        const landmarks = latestLandmarksRef.current;
        if (landmarks && landmarks.length >= 2 && scalingModeRef.current.initialDistance) {
          // Get current positions of both hands
          const hand1IndexTip = landmarks[0][8];
          const hand2IndexTip = landmarks[1][8];

          const hand1Pos = {
            x: (1 - hand1IndexTip.x) * width,
            y: (1 - hand1IndexTip.y) * height
          };
          const hand2Pos = {
            x: (1 - hand2IndexTip.x) * width,
            y: (1 - hand2IndexTip.y) * height
          };

          // Calculate current distance between hands
          const currentDistance = Math.hypot(hand2Pos.x - hand1Pos.x, hand2Pos.y - hand1Pos.y);

          // Calculate scale factor
          const scaleFactor = currentDistance / scalingModeRef.current.initialDistance;

          // Apply scaling to all drawing dots
          drawingDotsRef.current.forEach((dot, index) => {
            if (scalingModeRef.current.initialDrawingPositions[index] && scalingModeRef.current.initialDrawingSizes[index]) {
              const initialPos = scalingModeRef.current.initialDrawingPositions[index];
              const initialSize = scalingModeRef.current.initialDrawingSizes[index];
              const centerPoint = scalingModeRef.current.centerPoint;

              // Scale position relative to center point
              const relativeX = initialPos.x - centerPoint.x;
              const relativeY = initialPos.y - centerPoint.y;
              const newX = centerPoint.x + (relativeX * scaleFactor);
              const newY = centerPoint.y + (relativeY * scaleFactor);

              // Scale size
              const newSize = initialSize * scaleFactor;

              // Update dot position and size
              dot.x = newX;
              dot.y = newY;

              // Update mesh position and scale in Three.js scene
              if (dot.mesh) {
                dot.mesh.position.set(newX, newY, 0);

                // Update geometry with new size
                if (dot.mesh.geometry) {
                  dot.mesh.geometry.dispose();
                  dot.mesh.geometry = new THREE.CircleGeometry(newSize, 14);
                }
              }
            }
          });

          //console.log(`Scaling: ${scaleFactor.toFixed(2)}x (Distance: ${currentDistance.toFixed(1)}px)`);
        }
      }
    }

    if (mode === 'Drawing' && !isThicknessPicking) {
      if (handInfo.length > 0) {
        const landmarks = latestLandmarksRef.current ? latestLandmarksRef.current[0] : null;
        if (landmarks) {
          const tip = landmarks[8];
          const x = (1 - tip.x) * width;
          const y = (1 - tip.y) * height;
          const z = 0;
          // Smoothing
          let smoothed = smoothedTipRef.current;
          if (!smoothed) {
            smoothed = { x, y, z };
          } else {
            smoothed = {
              x: DRAWING_CONFIG.SMOOTHING_ALPHA * smoothed.x + (1 - DRAWING_CONFIG.SMOOTHING_ALPHA) * x,
              y: DRAWING_CONFIG.SMOOTHING_ALPHA * smoothed.y + (1 - DRAWING_CONFIG.SMOOTHING_ALPHA) * y,
              z: DRAWING_CONFIG.SMOOTHING_ALPHA * smoothed.z + (1 - DRAWING_CONFIG.SMOOTHING_ALPHA) * z,
            };
          }
          smoothedTipRef.current = smoothed;
          // Only add a dot if far enough from last dot
          const lastDot = drawingDotsRef.current.length > 0 ? drawingDotsRef.current[drawingDotsRef.current.length - 1] : null;
          const dist = lastDot ? Math.hypot(lastDot.x - smoothed.x, lastDot.y - smoothed.y) : 999;
          const minDist = DRAWING_CONFIG.MIN_DISTANCE;
          const maxDist = DRAWING_CONFIG.MAX_DISTANCE;
          // Time-based logic for auto-fill decision and drawing throttle
          const currentTime = performance.now();
          const timeSinceLastPoint = currentTime - lastDrawPointTimeRef.current;
          const maxTimeBetweenPoints = DRAWING_CONFIG.MAX_TIME_BETWEEN_POINTS;

          // Drawing throttle - limit drawing frequency for better performance
          if (timeSinceLastPoint < DRAWING_CONFIG.DRAW_THROTTLE) {
            return; // Skip this frame to maintain performance
          }

          if (!lastDot || dist > minDist) {
            // Decide whether to auto-fill based on both distance AND time
            const shouldAutoFill = lastDot &&
                                  dist > minDist &&
                                  dist <= maxDist &&
                                  timeSinceLastPoint <= maxTimeBetweenPoints;

            if (shouldAutoFill) {
              const steps = Math.floor(dist / minDist);
              for (let i = 1; i <= steps; i++) {
                const t = i / (steps + 1);
                const interpX = lastDot.x + (smoothed.x - lastDot.x) * t;
                const interpY = lastDot.y + (smoothed.y - lastDot.y) * t;
                const dotGeom = new THREE.CircleGeometry(thicknessValue/2, 14);
                const colorHex = new THREE.Color(selectedColor);
                const dotMat = new THREE.MeshBasicMaterial({ color: colorHex });
                const dotMesh = new THREE.Mesh(dotGeom, dotMat);
                dotMesh.position.set(interpX, interpY, 0);
                threeSceneRef.current.add(dotMesh);
                drawingDotsRef.current.push({ x: interpX, y: interpY, z: 0, mesh: dotMesh });
              }
            }

            // Add the actual new dot
            const dotGeom = new THREE.CircleGeometry(thicknessValue/2, 14);
            const colorHex = new THREE.Color(selectedColor);
            const dotMat = new THREE.MeshBasicMaterial({ color: colorHex });
            const dotMesh = new THREE.Mesh(dotGeom, dotMat);
            dotMesh.position.set(smoothed.x, smoothed.y, 0);
            threeSceneRef.current.add(dotMesh);
            drawingDotsRef.current.push({ x: smoothed.x, y: smoothed.y, z: 0, mesh: dotMesh });

            // Update the timestamp for the last point
            lastDrawPointTimeRef.current = currentTime;
          }
        }
      }
    } else {
      smoothedTipRef.current = null;
    }
    // --- ERASE LOGIC ---
    if (mode === 'Erasing') {
      if (handInfo.length > 0 && currentGestures[0] === 'Erasing') {
        const landmarks = latestLandmarksRef.current ? latestLandmarksRef.current[0] : null;
        if (landmarks) {
          const tipA = landmarks[8];
          const tipB = landmarks[12];
          const xA = (1 - tipA.x) * width;
          const yA = (1 - tipA.y) * height;
          const xB = (1 - tipB.x) * width;
          const yB = (1 - tipB.y) * height;
          const pointA = { x: xA, y: yA };
          const pointB = { x: xB, y: yB };
          const eraserRadius = Math.max(8, 0.3 * Math.hypot(xA - xB, yA - yB));
          // Update eraser visual overlay
          const eraserLength = Math.hypot(xA - xB, yA - yB);
          const centerX = (xA + xB) / 2;
          const centerY = ((tipA.y + tipB.y) * height) / 2;
          const angle = Math.atan2(yB - yA, xB - xA) * 180 / Math.PI;
          setEraserVisual({
            x: centerX,
            y: centerY,
            width: eraserRadius * 2,
            height: eraserLength,
            angle: -angle + 90,
            visible: true
          });
          // Helper: distance from point to segment
          function pointToSegmentDistance(pt, a, b) {
            const abx = b.x - a.x;
            const aby = b.y - a.y;
            const apx = pt.x - a.x;
            const apy = pt.y - a.y;
            const abLenSq = abx * abx + aby * aby;
            const t = abLenSq === 0 ? 0 : Math.max(0, Math.min(1, (apx * abx + apy * aby) / abLenSq));
            const projx = a.x + abx * t;
            const projy = a.y + aby * t;
            return Math.hypot(pt.x - projx, pt.y - projy);
          }
          // Remove dots within eraser
          for (let i = drawingDotsRef.current.length - 1; i >= 0; i--) {
            const dot = drawingDotsRef.current[i];
            if (pointToSegmentDistance(dot, pointA, pointB) <= eraserRadius) {
              if (dot.mesh) {
                threeSceneRef.current.remove(dot.mesh);
                dot.mesh.geometry.dispose();
                dot.mesh.material.dispose();
              }
              drawingDotsRef.current.splice(i, 1);
            }
          }
        }
      }
    } else {
      setEraserVisual(null);
    }
  }, [mode, handInfo, currentGestures, isThicknessPicking]);

  // Helper: convert hue/sat/val to hex
  function hsvToHex(h, s, v) {
    return tinycolor({ h, s: s * 100, v: v * 100 }).toHexString();
  }

  // Helper: convert hue/sat/val to hex
  function hsvToHex(h, s, v) {
    return tinycolor({ h, s: s * 100, v: v * 100 }).toHexString();
  }

  // When color picking mode is activated, snap picker beside face (mirrored X, further right)
  useEffect(() => {
    if (isColorPicking && headBarPos) {
      setColorPickerPos({
        x: width - (headBarPos.x + 60), // was +120, now closer to head
        y: headBarPos.y + 20, // was +40, now a bit higher
      });
    }
  }, [isColorPicking, headBarPos]);

  // While pinching, update selector position only (not picker position)
  useEffect(() => {
    if (!isColorPicking || !colorPickerPos) return;
    const updateSelector = () => {
      if (!latestLandmarksRef.current || !latestLandmarksRef.current[0]) return;
      const thumbTip = latestLandmarksRef.current[0][4];
      const indexTip = latestLandmarksRef.current[0][8];
      const x1 = (1 - thumbTip.x) * width;
      const y1 = thumbTip.y * height;
      const x2 = (1 - indexTip.x) * width;
      const y2 = indexTip.y * height;
      const colorMidX = (x1 + x2) / 2;
      const colorMidY = (y1 + y2) / 2;
      const pinchDist = Math.hypot(x1 - x2, y1 - y2);
      // Check if pinch is inside hue/sat square
      if (pinchDist < 40) {
        const pickerLeft = colorPickerPos.x;
        const pickerTop = colorPickerPos.y;
        if (
          colorMidX >= pickerLeft && colorMidX <= pickerLeft + colorPickerConfig.size &&
          colorMidY >= pickerTop && colorMidY <= pickerTop + colorPickerConfig.size
        ) {
          // Update selector in hue/sat square
          const relX = (colorMidX - pickerLeft) / colorPickerConfig.size;
          const relY = (colorMidY - pickerTop) / colorPickerConfig.size;
          setColorPickerSelector(sel => ({ ...sel, x: relX, y: relY }));
        } else if (
          colorMidX >= pickerLeft + colorPickerConfig.size + 20 && // adjust slider offset
          colorMidX <= pickerLeft + colorPickerConfig.size + 20 + colorPickerConfig.sliderWidth &&
          colorMidY >= pickerTop &&
          colorMidY <= pickerTop + colorPickerConfig.size
        ) {
          // Update brightness slider
          const relV = 1 - (colorMidY - pickerTop) / colorPickerConfig.size;
          setColorPickerSelector(sel => ({ ...sel, v: relV }));
        }
      }
    };
    let raf;
    const loop = () => {
      updateSelector();
      raf = requestAnimationFrame(loop);
    };
    loop();
    return () => cancelAnimationFrame(raf);
  }, [isColorPicking, colorPickerPos]);

  // Update selected color when selector changes
  useEffect(() => {
    const h = colorPickerSelector.x * 360;
    const s = 1 - colorPickerSelector.y;
    const v = colorPickerSelector.v;
    setSelectedColor(hsvToHex(h, s, v));
  }, [colorPickerSelector]);

  // Hide color picker and log color when mode changes to Back
  useEffect(() => {
    if (mode === 'Back' && isColorPicking) {
      setIsColorPicking(false);
    }
  }, [mode, isColorPicking, selectedColor]);

  // F8 debug toggle
  useEffect(() => {
    const handler = (e) => {
      if (e.key === 'F8') setShowDebugUI(v => !v);
    };
    window.addEventListener('keydown', handler);
    return () => window.removeEventListener('keydown', handler);
  }, []);

  // Thickness Picker: follow pinch position if pinched
  useEffect(() => {
    if (isThicknessPicking && headBarPos) {
      setThicknessPickerPos({
        x: width - (headBarPos.x + 37),
        y: headBarPos.y + 40,
      });
    }
  }, [isThicknessPicking, headBarPos]);

  // Only move the slider handle and preview with the pinch
  useEffect(() => {
    if (!isThicknessPicking || !thicknessPickerPos) return;
    let lastRelY = thicknessSliderRelY;
    let lastValue = thicknessValue;
    const updateSlider = () => {
      if (!latestLandmarksRef.current || !latestLandmarksRef.current[0]) return;
      const thumbTip = latestLandmarksRef.current[0][4];
      const indexTip = latestLandmarksRef.current[0][8];
      const x1 = (1 - thumbTip.x) * width;
      const y1 = thumbTip.y * height;
      const x2 = (1 - indexTip.x) * width;
      const y2 = indexTip.y * height;
      const thicknessMidX = (x1 + x2) / 2;
      const thicknessMidY = (y1 + y2) / 2;
      const pinchDist = Math.hypot(x1 - x2, y1 - y2);
      const pickerLeft = thicknessPickerPos.x;
      const pickerTop = thicknessPickerPos.y;
      if (pinchDist < 40) {
        let relY = (thicknessMidY - pickerTop) / thicknessConfig.sliderHeight;
        relY = Math.max(0, Math.min(1, relY));
        setThicknessSliderRelY(relY);
        lastRelY = relY;
        const value = Math.round(thicknessConfig.min + (1 - relY) * (thicknessConfig.max - thicknessConfig.min));
        setThicknessValue(Math.max(thicknessConfig.min, Math.min(thicknessConfig.max, value)));
        lastValue = value;
      } else {
        setThicknessSliderRelY(lastRelY);
        setThicknessValue(lastValue);
      }
    };
    let raf;
    const loop = () => { updateSlider(); raf = requestAnimationFrame(loop); };
    loop();
    return () => cancelAnimationFrame(raf);
  }, [isThicknessPicking, thicknessPickerPos]);

  // When thickness picking is exited, sync relY to thicknessValue
  useEffect(() => {
    if (!isThicknessPicking) {
      setThicknessSliderRelY(1 - (thicknessValue - thicknessConfig.min) / (thicknessConfig.max - thicknessConfig.min));
    }
  }, [isThicknessPicking, thicknessValue]);

  // Hide pickers on Back gesture
  useEffect(() => {
    if (mode === 'Back' && isColorPicking) setIsColorPicking(false);
    if (mode === 'Back' && isThicknessPicking) setIsThicknessPicking(false);
  }, [mode, isColorPicking, isThicknessPicking]);

  return (
    <div className="hand-gesture-container" style={{ position: 'relative', width: `${width}px`, height: `${height}px` }}>
      {/* Three.js overlay canvas */}
      <canvas
        ref={threeCanvasRef}
        className="three-canvas"
        style={{ position: 'absolute', left: 0, top: 0, width: '100%', height: '100%' }}
        width={width}
        height={height}
      />
      {/* Eraser visual (CSS/React) */}
      {eraserVisual && eraserVisual.visible && (
        <div
          className="eraser-visual"
          style={{
            position: 'absolute',
            left: eraserVisual.x - eraserVisual.width / 2,
            top: eraserVisual.y - eraserVisual.height / 2,
            width: eraserVisual.width,
            height: eraserVisual.height,
            borderRadius: eraserVisual.width,
            pointerEvents: 'none',
            zIndex: 50,
            transform: `rotate(${eraserVisual.angle}deg)` + ' scale(1)',
          }}
        />
      )}
      {/* Mode display at top left */}
      {showDebugUI && (
        <div style={{ position: 'absolute', top: 16, left: 16, zIndex: 40, background: 'rgba(0,0,0,0.7)', color: 'white', padding: '8px 20px', borderRadius: 8, fontWeight: 'bold', fontSize: 22, boxShadow: '0 2px 8px #0003' }}>
          Mode: {mode}
        </div>
      )}
      {isLoading && (
        <div className="loading-overlay">
          <div className="loading-spinner"></div>
          <p>Initializing camera...</p>
        </div>
      )}
      {error && (
        <div className="loading-overlay" style={{background: 'rgba(255,0,0,0.8)'}}>
          <p style={{color: 'white', fontWeight: 'bold'}}>{error}</p>
        </div>
      )}
      <video
        ref={videoRef}
        className="video-element"
        width="960"
        height="720"
      />
      <canvas
        ref={canvasRef}
        className="canvas-element"
        width="960"
        height="720"
      />
      {headBarPos && (
        <div
          className="headbar"
          style={{
            position: 'absolute',
            zIndex: 30,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            flexDirection: 'row',
            gap: 20,
            ...getHeadBarPosition()
          }}
        >
          <div className="color-wheel">
            <img src={colorWheel} alt="color-wheel" className="color-wheel-icon" />
          </div>

          <div className="thickness">
            <img src={thickness} alt="thickness" className="thickness-icon" />
          </div>
        </div>
      )}
      {showDebugUI && (
        <div style={{ position: 'absolute', bottom: 16, left: 16, zIndex: 40, background: 'rgba(0,0,0,0.7)', color: 'white', padding: '12px 20px', borderRadius: 8, minWidth: 220, boxShadow: '0 2px 8px #0003' }}>
          <h4 style={{margin: 0, marginBottom: 8, fontWeight: 'bold'}}>Hand Debug Info</h4>
          {handInfo.length > 0 ? (
            <ul style={{margin: 0, padding: 0, listStyle: 'none'}}>
              {handInfo.map((info, idx) => (
                <li key={idx} style={{marginBottom: 6}}>
                  <span style={{fontWeight: 'bold'}}>Hand {idx + 1}:</span> {info.handedness} | Gesture: {currentGestures[idx] || 'None'} | Orientation: {info.orientation}
                </li>
              ))}
            </ul>
          ) : (
            <span>No hands detected</span>
          )}
        </div>
      )}
      {handInfo.length > 0 && (
        <div className="hand-info-list" style={{ display: 'none' }}>
          {/* Hide the old hand info list, now included in debug card */}
        </div>
      )}
      {isColorPicking && colorPickerPos && (() => {
        // Color picker position
        const pickerLeft = colorPickerPos.x;
        const pickerTop = colorPickerPos.y;
        // Selector position
        const selX = pickerLeft + colorPickerSelector.x * colorPickerConfig.size;
        const selY = pickerTop + colorPickerSelector.y * colorPickerConfig.size;
        // Brightness slider
        const sliderLeft = pickerLeft + colorPickerConfig.size + 20; // adjust slider offset
        const sliderTop = pickerTop;
        const sliderY = sliderTop + (1 - colorPickerSelector.v) * colorPickerConfig.size;
        // Color for slider
        const h = colorPickerSelector.x * 360;
        const s = colorPickerSelector.y;
        // Render hue/sat square as a canvas (no brightness applied)
        return (
          <div
            className="color-picker"
            style={{
              position: 'absolute',
              left: pickerLeft,
              top: pickerTop,
              width: colorPickerConfig.size + colorPickerConfig.sliderWidth + 32,
              height: colorPickerConfig.size + 32,
              padding: 12,
              pointerEvents: 'none',
            }}
          >
            {/* Hue/Sat Canvas */}
            <ColorPickerCanvas
              className="color-picker__canvas"
              width={colorPickerConfig.size}
              height={colorPickerConfig.size}
            />
            {/* Selector circle */}
            <div
              className="color-picker__selector"
              style={{
                position: 'absolute',
                left: selX - pickerLeft - 12,
                top: selY - pickerTop - 12,
                width: 24,
                height: 24,
                pointerEvents: 'none',
              }}
            />
            {/* Brightness slider */}
            <div
              className="color-picker__slider"
              style={{
                position: 'absolute',
                left: sliderLeft - pickerLeft,
                top: sliderTop - pickerTop,
                width: colorPickerConfig.sliderWidth,
                height: colorPickerConfig.size,
                background: `linear-gradient(to top, #000, ${hsvToHex(h, s, 1)})`,
                pointerEvents: 'none',
                marginTop: 14,
              }}
            />
            {/* Slider handle */}
            <div
              className="color-picker__slider-handle"
              style={{
                position: 'absolute',
                left: sliderLeft - pickerLeft - 4,
                top: sliderY - pickerTop - 8,
                width: colorPickerConfig.sliderWidth + 8,
                height: 16,
                pointerEvents: 'none',
                marginTop: 14,

              }}
            />
          </div>
        );
      })()}
      {/* Unified color+thickness preview bubble */}
      <div
        className="color-preview-circle"
        style={{
          position: 'absolute',
          top: 24,
          right: 32,
          width: thicknessValue * 2.2,
          height: thicknessValue * 2.2,
          background: selectedColor,
          zIndex: 120,
          borderRadius: '50%',
          border: '2.5px solid #888',
          boxShadow: '0 0 8px #000a',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      />
      {/* Restore thickness picker UI */}
      {isThicknessPicking && thicknessPickerPos && (
        <div
          className="thickness-picker"
          style={{
            left: thicknessPickerPos.x,
            top: thicknessPickerPos.y,
          }}
        >
          <div className="thickness-picker__slider" style={{ width: thicknessConfig.sliderWidth, height: thicknessConfig.sliderHeight }}>
            {/* Handle */}
            <div
              className="thickness-picker__handle"
              style={{
                top: `${thicknessSliderRelY * (thicknessConfig.sliderHeight - 9)}px`,
                background: '#fff',
                borderColor: '#bbb',
              }}
            />
            {/* Visual thickness preview on slider */}
            <div
              style={{
                position: 'absolute',
                left: '50%',
                top: `${thicknessSliderRelY * (thicknessConfig.sliderHeight - 9) + 9}px`,
                transform: 'translate(-50%, -50%)',
                width: thicknessValue * 1.2,
                height: thicknessValue * 1.2,
                borderRadius: '50%',
                background: selectedColor,
                border: '2px solid #888',
                boxShadow: '0 0 8px #000a',
                pointerEvents: 'none',
              }}
            />
          </div>
          <div style={{ color: '#fff', marginTop: 10, fontWeight: 'bold', fontSize: 18 }}>
            {thicknessValue}px
          </div>
        </div>
      )}
      {/* Render finger tip pointers when picking color or thickness */}
      {(isColorPicking || isThicknessPicking) && latestLandmarksRef.current && latestLandmarksRef.current[0] && (
        <>
          {/* Thumb tip pointer */}
          <div
            className="finger-pointer thumb"
            style={{
              left: `${(1 - latestLandmarksRef.current[0][4].x) * width - 11}px`,
              top: `${latestLandmarksRef.current[0][4].y * height - 11}px`,
            }}
          />
          {/* Index tip pointer */}
          <div
            className="finger-pointer index"
            style={{
              left: `${(1 - latestLandmarksRef.current[0][8].x) * width - 11}px`,
              top: `${latestLandmarksRef.current[0][8].y * height - 11}px`,
            }}
          />
        </>
      )}

      {/* Word Selection Overlay */}
      {isWordSelectionMode && wordPositions.length > 0 && (
        <div className="word-selection-overlay-gesture" style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          zIndex: 200,
          pointerEvents: 'none'
        }}>
          {/* Blur overlay */}
          <div style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            background: 'rgba(255, 255, 255, 0.1)',
            backdropFilter: 'blur(8px)',
            zIndex: -1
          }} />
          {/* Timer display */}
          <div style={{
            position: 'absolute',
            top: '40px',
            left: '50%',
            transform: 'translateX(-50%)',
            background: '#ff6b6b',
            color: 'white',
            padding: '16px 32px',
            borderRadius: '25px',
            fontSize: '16px',
            fontWeight: '600',
            boxShadow: '0 4px 20px rgba(255, 107, 107, 0.4)',
            textAlign: 'center',
            minWidth: '200px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: '12px'
          }}>
            <div style={{ fontSize: '14px', opacity: '0.9' }}>Time Left</div>
            <div style={{ fontSize: '24px', fontWeight: '700', color: '#ffff00' }}>{wordSelectionTimeLeft}s</div>
          </div>

          {/* Word options */}
          {wordPositions.map((wordPos, index) => {
            const isSelected = selectedWord === wordPos.word;
            const isSelectingWord = selectedWord === wordPos.word && isSelecting;
            const isHovering = isPinching && pinchPosition &&
              pinchPosition.x >= (wordPos.x - wordPos.width / 2) &&
              pinchPosition.x <= (wordPos.x + wordPos.width / 2) &&
              pinchPosition.y >= (wordPos.y - wordPos.height / 2) &&
              pinchPosition.y <= (wordPos.y + wordPos.height / 2);

            return (
              <div
                key={wordPos.word}
                style={{
                  position: 'absolute',
                  left: wordPos.x - wordPos.width / 2,
                  top: wordPos.y - wordPos.height / 2,
                  width: wordPos.width,
                  height: wordPos.height,
                  background: isSelected ?
                    '#4ecdc4' :
                    isHovering ?
                    'rgba(78, 205, 196, 0.3)' :
                    'rgba(50, 50, 50, 0.9)',
                  border: isSelected ?
                    '3px solid #4ecdc4' :
                    isHovering ?
                    '3px solid rgba(78, 205, 196, 0.6)' :
                    '3px solid rgba(100, 100, 100, 0.8)',
                  borderRadius: '25px',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  color: isSelected ? 'white' :
                         isHovering ? '#4ecdc4' : 'white',
                  backdropFilter: 'blur(10px)',
                  boxShadow: isSelected ?
                    '0 8px 32px rgba(78, 205, 196, 0.4)' :
                    isHovering ?
                    '0 6px 24px rgba(78, 205, 196, 0.2)' :
                    '0 4px 20px rgba(0, 0, 0, 0.3)',
                  transition: isSelected ? 'none' : 'all 0.2s ease',
                  transform: isSelected ? 'scale(1.05)' :
                            isHovering ? 'scale(1.02)' : 'scale(1)',
                  cursor: 'pointer'
                }}
              >
                <div style={{
                  fontSize: '18px',
                  fontWeight: '600',
                  textShadow: isSelected ? '0 2px 4px rgba(0, 0, 0, 0.3)' :
                             isHovering ? '0 2px 4px rgba(0, 0, 0, 0.2)' : 'none'
                }}>
                  {wordPos.word}
                </div>

                {isSelected && isSelectingWord && (
                  <div style={{
                    position: 'absolute',
                    bottom: '12px',
                    left: '50%',
                    transform: 'translateX(-50%)',
                    width: '10px',
                    height: '10px',
                    background: 'white',
                    borderRadius: '50%',
                    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3)'
                  }} />
                )}
              </div>
            );
          })}

          {/* Pinch visualization dot */}
          {isPinching && pinchPosition && (
            <div style={{
              position: 'absolute',
              left: pinchPosition.x - 8,
              top: pinchPosition.y - 8,
              width: '16px',
              height: '16px',
              background: 'rgba(255, 255, 0, 0.8)',
              border: '2px solid rgba(255, 255, 255, 0.9)',
              borderRadius: '50%',
              boxShadow: '0 0 20px rgba(255, 255, 0, 0.6)',
              zIndex: 1000,
              pointerEvents: 'none'
            }} />
          )}



          {/* Instructions */}
          <div style={{
            position: 'absolute',
            bottom: '40px',
            left: '50%',
            transform: 'translateX(-50%)',
            background: 'rgba(0, 0, 0, 0.8)',
            color: 'white',
            padding: '14px 28px',
            borderRadius: '25px',
            fontSize: '16px',
            fontWeight: '500',
            textAlign: 'center',
            backdropFilter: 'blur(10px)',
            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)'
          }}>
            Hold your pinch on a word for 2 seconds to select it
          </div>
        </div>
      )}

      {/* User Welcome Message */}
      {userData && userData.userName && !isWordSelectionMode && (
        <div
          className="user-welcome"
          style={{
            position: 'absolute',
            top: '20px',
            right: '20px',
            padding: '10px 20px',
            background: 'rgba(255, 255, 255, 0.1)',
            color: 'white',
            borderRadius: '25px',
            fontSize: '14px',
            fontWeight: '500',
            zIndex: 1000,
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255, 255, 255, 0.2)'
          }}
        >
          Welcome, {userData.userName}! 👋
        </div>
      )}
    </div>
  );
};

function ColorPickerCanvas({ width, height }) {
  const canvasRef = React.useRef();
  useEffect(() => {
    const ctx = canvasRef.current.getContext('2d');
    for (let y = 0; y < height; y++) {
      for (let x = 0; x < width; x++) {
        if (y === height - 1) {
          ctx.fillStyle = '#ffffff'; // bottom row is pure white
        } else {
          const hue = (x / width) * 360;
          const sat = 1 - (y / height);
          ctx.fillStyle = tinycolor({ h: hue, s: sat * 100, v: 100 }).toHexString();
        }
        ctx.fillRect(x, y, 1, 1);
      }
    }
  }, [width, height]);
  return <canvas ref={canvasRef} width={width} height={height} className="color-picker__canvas" style={{ pointerEvents: 'none' }} />;
}

export default HandGestureDetector; 
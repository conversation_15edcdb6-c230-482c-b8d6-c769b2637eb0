/* ScaledHandGestureDetector Wrapper Styles */

.scaled-hand-gesture-detector {
  /* Container that defines the final size */
  position: relative;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  background-color: #000;
  justify-self: center;
}

.scaled-hand-gesture-wrapper {
  /* Wrapper that applies the scaling transform */
  position: relative;
  display: block;
}

/* Ensure the inner HandGestureDetector doesn't add extra margins or positioning */
.scaled-hand-gesture-detector .hand-gesture-container {
  position: relative;
  margin: 0;
  border-radius: 0; /* Remove border radius since the outer container handles it */
  box-shadow: none; /* Remove shadow since the outer container handles it */
  background-color: transparent; /* Remove background since the outer container handles it */
}

/* Optional: Add some responsive behavior for very small scales */
.scaled-hand-gesture-detector.small-scale {
  /* You can add specific styles for small scales if needed */
}

.scaled-hand-gesture-detector.large-scale {
  /* You can add specific styles for large scales if needed */
}

/* Ensure proper scaling of all child elements */
.scaled-hand-gesture-wrapper * {
  /* Prevent any child elements from breaking out of the scaled container */
  box-sizing: border-box;
}

/* Handle potential issues with absolute positioned elements inside */
.scaled-hand-gesture-wrapper .gesture-status-list,
.scaled-hand-gesture-wrapper .headbar,
.scaled-hand-gesture-wrapper .color-picker,
.scaled-hand-gesture-wrapper .thickness-picker {
  /* These elements use absolute positioning and should scale properly */
  /* No additional styles needed as they inherit the transform */
}

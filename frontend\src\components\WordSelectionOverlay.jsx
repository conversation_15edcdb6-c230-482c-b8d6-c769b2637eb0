import React, { useState, useEffect, useRef } from 'react';
import './WordSelectionOverlay.css';

/**
 * WordSelectionOverlay - Displays word options over the gesture detector
 * Players pinch and hold for 2 seconds to select a word
 */
const WordSelectionOverlay = ({
  wordOptions = [],
  timeLeft = 20,
  onWordSelected,
  isVisible = true,
  disabled = false
}) => {
  const [selectedWord, setSelectedWord] = useState(null);
  const [selectionProgress, setSelectionProgress] = useState(0);
  const [isSelecting, setIsSelecting] = useState(false);
  const selectionTimerRef = useRef(null);
  const progressIntervalRef = useRef(null);

  // Clear timers on unmount
  useEffect(() => {
    return () => {
      if (selectionTimerRef.current) {
        clearTimeout(selectionTimerRef.current);
      }
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
      }
    };
  }, []);

  // Handle pinch start (mouse down or touch start)
  const handleSelectionStart = (word) => {
    if (disabled || !isVisible) return;
    
    console.log('🤏 Starting word selection for:', word);
    setSelectedWord(word);
    setIsSelecting(true);
    setSelectionProgress(0);

    // Start progress animation
    progressIntervalRef.current = setInterval(() => {
      setSelectionProgress(prev => {
        const newProgress = prev + (100 / 20); // 2 seconds = 20 intervals of 100ms
        return Math.min(newProgress, 100);
      });
    }, 100);

    // Set timer for 2 seconds
    selectionTimerRef.current = setTimeout(() => {
      console.log('✅ Word selected:', word);
      setIsSelecting(false);
      setSelectionProgress(100);
      onWordSelected?.(word);
    }, 2000);
  };

  // Handle pinch end (mouse up or touch end)
  const handleSelectionEnd = () => {
    if (!isSelecting) return;
    
    console.log('🤏 Selection cancelled');
    setIsSelecting(false);
    setSelectedWord(null);
    setSelectionProgress(0);

    // Clear timers
    if (selectionTimerRef.current) {
      clearTimeout(selectionTimerRef.current);
      selectionTimerRef.current = null;
    }
    if (progressIntervalRef.current) {
      clearInterval(progressIntervalRef.current);
      progressIntervalRef.current = null;
    }
  };

  if (!isVisible) return null;

  return (
    <div className="word-selection-overlay">
      {/* Timer display */}
      <div className="word-selection-timer">
        <span className="timer-text">Choose your word: {timeLeft}s</span>
      </div>

      {/* Word options - horizontal layout */}
      <div className="word-options-horizontal">
        {wordOptions.map((word, index) => (
          <div
            key={word}
            className={`word-option ${selectedWord === word ? 'selecting' : ''} ${disabled ? 'disabled' : ''}`}
            data-word={word}
            style={{
              '--selection-progress': `${selectedWord === word ? selectionProgress : 0}%`
            }}
          >
            <div className="word-text">{word}</div>
            {selectedWord === word && isSelecting && (
              <div className="selection-progress">
                <div
                  className="progress-bar"
                  style={{ width: `${selectionProgress}%` }}
                />
              </div>
            )}
            <div className="pinch-instruction">
              Pinch & hold for 2s
            </div>
          </div>
        ))}
      </div>

      {/* Instructions */}
      <div className="word-selection-instructions">
        <p>Pinch and hold a word for 2 seconds to select it</p>
        <p>If time runs out, a word will be chosen randomly</p>
      </div>
    </div>
  );
};

export default WordSelectionOverlay;
